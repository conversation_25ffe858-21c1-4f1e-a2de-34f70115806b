/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/checker/results/route";
exports.ids = ["app/api/checker/results/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "module":
/*!*************************!*\
  !*** external "module" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("module");

/***/ }),

/***/ "postgres":
/*!***************************!*\
  !*** external "postgres" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = import("postgres");;

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchecker%2Fresults%2Froute&page=%2Fapi%2Fchecker%2Fresults%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchecker%2Fresults%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchecker%2Fresults%2Froute&page=%2Fapi%2Fchecker%2Fresults%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchecker%2Fresults%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_checker_results_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/checker/results/route.ts */ \"(rsc)/./src/app/api/checker/results/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([C_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_checker_results_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nC_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_checker_results_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/checker/results/route\",\n        pathname: \"/api/checker/results\",\n        filename: \"route\",\n        bundlePath: \"app/api/checker/results/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\api\\\\checker\\\\results\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_checker_results_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchecker%2Fresults%2Froute&page=%2Fapi%2Fchecker%2Fresults%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchecker%2Fresults%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/checker/results/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/checker/results/route.ts ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db/index.ts\");\n/* harmony import */ var _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/db/schema */ \"(rsc)/./src/lib/db/schema.ts\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n/* harmony import */ var _lib_ai_service__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/ai-service */ \"(rsc)/./src/lib/ai-service.ts\");\n/* harmony import */ var _lib_certificate_generator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/certificate-generator */ \"(rsc)/./src/lib/certificate-generator.ts\");\n/* harmony import */ var _lib_utils_certificate__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils/certificate */ \"(rsc)/./src/lib/utils/certificate.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_auth__WEBPACK_IMPORTED_MODULE_1__, _lib_db__WEBPACK_IMPORTED_MODULE_2__]);\n([_lib_auth__WEBPACK_IMPORTED_MODULE_1__, _lib_db__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n// Helper function to auto-generate AI feedback and certificate\nasync function autoGenerateAIFeedbackAndCertificate(resultId) {\n    try {\n        console.log(`Auto-generating AI feedback and certificate for result: ${resultId}`);\n        // Get test result with candidate info using new schema structure\n        const result = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select({\n            testResult: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults,\n            candidate: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates,\n            testRegistration: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations\n        }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults).innerJoin(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.testRegistrationId, _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations.id)).innerJoin(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations.candidateId, _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.id)).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.id, resultId)).limit(1);\n        if (!result.length) {\n            console.error('Test result not found for auto-generation:', resultId);\n            return;\n        }\n        const { testResult, candidate } = result[0];\n        // Generate AI Feedback if not already generated\n        if (!testResult.aiFeedbackGenerated) {\n            try {\n                console.log('Generating AI feedback...');\n                const feedbackData = await (0,_lib_ai_service__WEBPACK_IMPORTED_MODULE_4__.generateAIFeedback)(testResult, candidate);\n                // Save the feedback to database\n                await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.insert(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.aiFeedback).values({\n                    testResultId: resultId,\n                    listeningFeedback: feedbackData.listeningFeedback,\n                    readingFeedback: feedbackData.readingFeedback,\n                    writingFeedback: feedbackData.writingFeedback,\n                    speakingFeedback: feedbackData.speakingFeedback,\n                    overallFeedback: feedbackData.overallFeedback,\n                    studyRecommendations: feedbackData.recommendations,\n                    strengths: feedbackData.strengths,\n                    weaknesses: feedbackData.weaknesses,\n                    studyPlan: feedbackData.studyPlan\n                });\n                console.log('AI feedback generated successfully');\n            } catch (error) {\n                console.error('Error generating AI feedback:', error);\n            }\n        }\n        // Generate Certificate if not already generated\n        if (!testResult.certificateGenerated) {\n            try {\n                console.log('Generating certificate...');\n                // Generate certificate serial if not exists\n                let certificateSerial = testResult.certificateSerial;\n                if (!certificateSerial) {\n                    certificateSerial = (0,_lib_utils_certificate__WEBPACK_IMPORTED_MODULE_6__.generateCertificateSerial)();\n                }\n                // Generate the certificate PDF\n                await (0,_lib_certificate_generator__WEBPACK_IMPORTED_MODULE_5__.generateCertificate)(testResult, candidate);\n                console.log('Certificate generated successfully');\n            } catch (error) {\n                console.error('Error generating certificate:', error);\n            }\n        }\n        // Update the test result to mark both as generated\n        const updateData = {\n            updatedAt: new Date()\n        };\n        if (!testResult.aiFeedbackGenerated) {\n            updateData.aiFeedbackGenerated = true;\n        }\n        if (!testResult.certificateGenerated) {\n            updateData.certificateGenerated = true;\n            if (!testResult.certificateSerial) {\n                updateData.certificateSerial = (0,_lib_utils_certificate__WEBPACK_IMPORTED_MODULE_6__.generateCertificateSerial)();\n            }\n        }\n        await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.update(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults).set(updateData).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.id, resultId));\n        console.log('Auto-generation completed for result:', resultId);\n    } catch (error) {\n        console.error('Error in auto-generation process:', error);\n    }\n}\nasync function POST(request) {\n    try {\n        const session = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.auth)();\n        if (!session) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const data = await request.json();\n        // Validate required fields\n        if (!data.testRegistrationId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Test Registration ID is required'\n            }, {\n                status: 400\n            });\n        }\n        // Check if test registration exists\n        const testRegistration = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations).innerJoin(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations.candidateId, _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.id)).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations.id, data.testRegistrationId)).limit(1);\n        if (!testRegistration.length) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Test registration not found'\n            }, {\n                status: 404\n            });\n        }\n        // Check if results already exist for this test registration\n        const existingResults = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.testRegistrationId, data.testRegistrationId)).limit(1);\n        const isUpdate = existingResults.length > 0;\n        // Convert string values to numbers where needed\n        const processedData = {\n            testRegistrationId: data.testRegistrationId,\n            // Listening scores (keep as strings for decimal fields)\n            listeningScore: data.listeningScore || null,\n            listeningBandScore: data.listeningBandScore || null,\n            // Reading scores\n            readingScore: data.readingScore || null,\n            readingBandScore: data.readingBandScore || null,\n            // Writing scores\n            writingTask1Score: data.writingTask1Score || null,\n            writingTask2Score: data.writingTask2Score || null,\n            writingBandScore: data.writingBandScore || null,\n            // Speaking scores\n            speakingFluencyScore: data.speakingFluencyScore || null,\n            speakingLexicalScore: data.speakingLexicalScore || null,\n            speakingGrammarScore: data.speakingGrammarScore || null,\n            speakingPronunciationScore: data.speakingPronunciationScore || null,\n            speakingBandScore: data.speakingBandScore || null,\n            // Overall score\n            overallBandScore: data.overallBandScore || null,\n            // Metadata\n            status: data.status || 'pending',\n            enteredBy: session.user?.id,\n            updatedAt: new Date()\n        };\n        let result;\n        if (isUpdate) {\n            // Update existing result\n            result = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.update(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults).set(processedData).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.id, existingResults[0].id)).returning();\n        } else {\n            // Create new test result\n            result = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.insert(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults).values(processedData).returning();\n        }\n        const savedResult = result[0];\n        // Auto-generate AI feedback and certificate if result is completed\n        if (processedData.status === 'completed' && savedResult.overallBandScore) {\n            // Run in background without blocking the response\n            setImmediate(async ()=>{\n                try {\n                    await autoGenerateAIFeedbackAndCertificate(savedResult.id);\n                } catch (error) {\n                    console.error('Background generation failed:', error);\n                }\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(savedResult, {\n            status: isUpdate ? 200 : 201\n        });\n    } catch (error) {\n        console.error('Error creating test result:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET(request) {\n    try {\n        const session = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.auth)();\n        if (!session) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const page = parseInt(searchParams.get('page') || '1');\n        const limit = parseInt(searchParams.get('limit') || '20');\n        const status = searchParams.get('status');\n        const offset = (page - 1) * limit;\n        const userId = session.user?.id;\n        // Build where conditions\n        let whereConditions = (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.enteredBy, userId);\n        if (status) {\n            whereConditions = (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.enteredBy, userId);\n        // Add status filter if needed\n        }\n        // Get results with candidate info using new schema structure\n        const results = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select({\n            id: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.id,\n            testRegistrationId: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.testRegistrationId,\n            listeningBandScore: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.listeningBandScore,\n            readingBandScore: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.readingBandScore,\n            writingBandScore: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.writingBandScore,\n            speakingBandScore: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.speakingBandScore,\n            overallBandScore: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.overallBandScore,\n            status: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.status,\n            createdAt: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.createdAt,\n            candidate: {\n                id: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.id,\n                fullName: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.fullName,\n                passportNumber: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.passportNumber\n            },\n            testRegistration: {\n                candidateNumber: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations.candidateNumber,\n                testDate: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations.testDate,\n                testCenter: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations.testCenter\n            }\n        }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults).innerJoin(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.testRegistrationId, _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations.id)).innerJoin(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_7__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testRegistrations.candidateId, _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.id)).where(whereConditions).limit(limit).offset(offset).orderBy(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.createdAt);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            results,\n            page,\n            limit\n        });\n    } catch (error) {\n        console.error('Error fetching test results:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/checker/results/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/ai-service.ts":
/*!*******************************!*\
  !*** ./src/lib/ai-service.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateAIFeedback: () => (/* binding */ generateAIFeedback),\n/* harmony export */   generatePersonalizedFeedback: () => (/* binding */ generatePersonalizedFeedback)\n/* harmony export */ });\n/* harmony import */ var _anthropic_ai_sdk__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @anthropic-ai/sdk */ \"(rsc)/./node_modules/@anthropic-ai/sdk/index.mjs\");\n\nconst anthropic = new _anthropic_ai_sdk__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n    apiKey: process.env.ANTHROPIC_API_KEY\n});\nasync function generateAIFeedback(testResult, candidate) {\n    const prompt = `\nYou are an expert IELTS examiner and English language learning advisor. Please provide comprehensive feedback for the following IELTS test results:\n\nCandidate Information:\n- Name: ${candidate.fullName}\n- Nationality: ${candidate.nationality}\n- Test Date: ${candidate.testDate}\n\nTest Scores:\n- Listening: ${testResult.listeningBandScore}/9.0 (Raw: ${testResult.listeningScore})\n- Reading: ${testResult.readingBandScore}/9.0 (Raw: ${testResult.readingScore})\n- Writing: ${testResult.writingBandScore}/9.0 (Task 1: ${testResult.writingTask1Score}, Task 2: ${testResult.writingTask2Score})\n- Speaking: ${testResult.speakingBandScore}/9.0 (Fluency: ${testResult.speakingFluencyScore}, Lexical: ${testResult.speakingLexicalScore}, Grammar: ${testResult.speakingGrammarScore}, Pronunciation: ${testResult.speakingPronunciationScore})\n- Overall Band Score: ${testResult.overallBandScore}/9.0\n\nPlease provide:\n\n1. Detailed feedback for each skill (Listening, Reading, Writing, Speaking) - 2-3 sentences each\n2. Overall performance feedback - 3-4 sentences\n3. Specific recommendations for improvement - 4-5 actionable points\n4. List of 3-4 key strengths\n5. List of 3-4 areas for improvement\n6. A personalized study plan including:\n   - Recommended timeframe for improvement\n   - 3-4 focus areas\n   - 4-5 specific resources\n   - 5-6 practice activities\n\nFormat your response as JSON with the following structure:\n{\n  \"listeningFeedback\": \"...\",\n  \"readingFeedback\": \"...\",\n  \"writingFeedback\": \"...\",\n  \"speakingFeedback\": \"...\",\n  \"overallFeedback\": \"...\",\n  \"recommendations\": \"...\",\n  \"strengths\": [\"...\", \"...\", \"...\"],\n  \"weaknesses\": [\"...\", \"...\", \"...\"],\n  \"studyPlan\": {\n    \"timeframe\": \"...\",\n    \"focusAreas\": [\"...\", \"...\", \"...\"],\n    \"resources\": [\"...\", \"...\", \"...\", \"...\"],\n    \"practiceActivities\": [\"...\", \"...\", \"...\", \"...\", \"...\"]\n  }\n}\n\nMake the feedback constructive, specific, and encouraging. Focus on practical advice that the candidate can implement.\n`;\n    try {\n        const response = await anthropic.messages.create({\n            model: 'claude-3-sonnet-20240229',\n            max_tokens: 2000,\n            messages: [\n                {\n                    role: 'user',\n                    content: prompt\n                }\n            ]\n        });\n        const content = response.content[0];\n        if (content.type !== 'text') {\n            throw new Error('Unexpected response type from Claude');\n        }\n        // Parse the JSON response\n        const feedbackData = JSON.parse(content.text);\n        return feedbackData;\n    } catch (error) {\n        console.error('Error generating AI feedback:', error);\n        // Return fallback feedback if AI service fails\n        return {\n            listeningFeedback: `With a band score of ${testResult.listeningBandScore}, your listening skills show good comprehension abilities. Continue practicing with various accents and audio materials.`,\n            readingFeedback: `Your reading score of ${testResult.readingBandScore} indicates solid reading comprehension. Focus on improving speed and accuracy with different text types.`,\n            writingFeedback: `Your writing band score of ${testResult.writingBandScore} shows competent writing skills. Work on task achievement, coherence, and lexical resource.`,\n            speakingFeedback: `With a speaking score of ${testResult.speakingBandScore}, you demonstrate good oral communication. Continue practicing fluency and pronunciation.`,\n            overallFeedback: `Your overall band score of ${testResult.overallBandScore} reflects your current English proficiency level. With focused practice, you can improve your performance across all skills.`,\n            recommendations: 'Practice regularly with authentic IELTS materials, focus on your weaker skills, and consider taking a preparation course.',\n            strengths: [\n                'Good overall comprehension',\n                'Adequate vocabulary range',\n                'Basic grammar understanding'\n            ],\n            weaknesses: [\n                'Need more practice with complex structures',\n                'Improve accuracy',\n                'Enhance fluency'\n            ],\n            studyPlan: {\n                timeframe: '3-6 months of focused study',\n                focusAreas: [\n                    'Grammar accuracy',\n                    'Vocabulary expansion',\n                    'Test strategies'\n                ],\n                resources: [\n                    'Official IELTS materials',\n                    'Cambridge IELTS books',\n                    'Online practice tests',\n                    'English news websites'\n                ],\n                practiceActivities: [\n                    'Daily reading practice',\n                    'Speaking with native speakers',\n                    'Writing essays weekly',\n                    'Listening to podcasts',\n                    'Taking mock tests'\n                ]\n            }\n        };\n    }\n}\n// New function for the feedback API\nasync function generatePersonalizedFeedback(request) {\n    const { overallScore, listeningScore, readingScore, writingScore, speakingScore } = request;\n    // Check if Claude API key is available\n    const apiKey = process.env.ANTHROPIC_API_KEY;\n    if (!apiKey) {\n        console.warn('ANTHROPIC_API_KEY not found, using mock feedback');\n        return generateMockFeedback(request);\n    }\n    try {\n        // Prepare the prompt for Claude\n        const prompt = `You are an expert IELTS examiner and English language teacher. Generate personalized feedback for a student based on their IELTS test scores.\n\nTest Scores:\n- Overall Band Score: ${overallScore}\n- Listening: ${listeningScore || 'Not provided'}\n- Reading: ${readingScore || 'Not provided'}\n- Writing: ${writingScore || 'Not provided'}\n- Speaking: ${speakingScore || 'Not provided'}\n\nPlease provide detailed feedback in the following JSON format:\n{\n  \"overallAssessment\": \"A comprehensive assessment of the student's English proficiency level\",\n  \"strengths\": [\"List of 3-4 specific strengths based on the scores\"],\n  \"areasForImprovement\": [\"List of 3-4 specific areas that need improvement\"],\n  \"specificRecommendations\": {\n    \"listening\": \"Specific advice for improving listening skills\",\n    \"reading\": \"Specific advice for improving reading skills\",\n    \"writing\": \"Specific advice for improving writing skills\",\n    \"speaking\": \"Specific advice for improving speaking skills\"\n  },\n  \"studyPlan\": \"A detailed study plan recommendation based on the current level\",\n  \"nextSteps\": [\"List of 4-5 actionable next steps for improvement\"]\n}\n\nMake the feedback encouraging but honest, specific to the scores provided, and actionable. Consider the IELTS band descriptors when providing recommendations.`;\n        const response = await anthropic.messages.create({\n            model: 'claude-3-sonnet-20240229',\n            max_tokens: 2000,\n            messages: [\n                {\n                    role: 'user',\n                    content: prompt\n                }\n            ]\n        });\n        const content = response.content[0];\n        if (content.type !== 'text') {\n            throw new Error('Unexpected response type from Claude');\n        }\n        // Parse the JSON response\n        try {\n            const feedback = JSON.parse(content.text);\n            return feedback;\n        } catch (err) {\n            console.error('Failed to parse Claude response:', content.text, err);\n            throw new Error('Invalid response format from AI service');\n        }\n    } catch (error) {\n        console.error('Claude API error:', error);\n        // Fallback to mock feedback if API fails\n        return generateMockFeedback(request);\n    }\n}\nfunction generateMockFeedback(request) {\n    const { overallScore } = request;\n    // Mock feedback based on score ranges\n    if (overallScore >= 7.0) {\n        return {\n            overallAssessment: \"Excellent performance! You have demonstrated strong English proficiency across all skills. Your overall band score of \" + overallScore + \" indicates you are a competent user of English with good operational command of the language.\",\n            strengths: [\n                \"Strong overall command of English language\",\n                \"Good vocabulary range and accuracy in most contexts\",\n                \"Effective communication skills with minor inaccuracies\",\n                \"Ability to handle complex language situations\"\n            ],\n            areasForImprovement: [\n                \"Fine-tune advanced grammar structures for academic contexts\",\n                \"Expand specialized academic and professional vocabulary\",\n                \"Practice complex sentence formations and cohesive devices\",\n                \"Work on consistency across all four skills\"\n            ],\n            specificRecommendations: {\n                listening: \"Focus on academic lectures, complex discussions, and various English accents. Practice note-taking while listening to improve comprehension of detailed information.\",\n                reading: \"Practice with academic texts, research papers, and complex argumentative essays. Work on speed reading techniques while maintaining comprehension.\",\n                writing: \"Work on advanced essay structures, sophisticated argumentation, and academic writing conventions. Focus on task achievement and coherence.\",\n                speaking: \"Practice formal presentations, debates, and discussions on abstract topics. Work on fluency and natural expression of complex ideas.\"\n            },\n            studyPlan: \"Continue with advanced materials focusing on academic English. Dedicate 2-3 hours daily to practice, with emphasis on maintaining consistency across all skills. Use authentic materials like academic journals, TED talks, and formal debates.\",\n            nextSteps: [\n                \"Take regular practice tests to maintain performance level\",\n                \"Focus on any weaker skills to achieve balance across all areas\",\n                \"Consider advanced English courses or academic preparation programs\",\n                \"Practice with time constraints to improve efficiency\",\n                \"Engage with native speakers in academic or professional contexts\"\n            ]\n        };\n    } else if (overallScore >= 5.5) {\n        return {\n            overallAssessment: \"Good foundation with room for improvement in specific areas. Your overall band score of \" + overallScore + \" shows you are a modest user of English with partial command of the language, coping with overall meaning in most situations.\",\n            strengths: [\n                \"Basic communication skills are well-established\",\n                \"Understanding of fundamental grammar structures\",\n                \"Adequate vocabulary for everyday and familiar situations\",\n                \"Ability to express basic ideas and opinions clearly\"\n            ],\n            areasForImprovement: [\n                \"Expand vocabulary range for academic and professional contexts\",\n                \"Improve complex grammar usage and sentence structures\",\n                \"Enhance fluency and coherence in extended discourse\",\n                \"Develop better accuracy in language use\"\n            ],\n            specificRecommendations: {\n                listening: \"Practice with various accents, speeds, and contexts. Focus on understanding main ideas and specific details in academic and social situations.\",\n                reading: \"Work on skimming and scanning techniques. Practice with longer texts and improve vocabulary through extensive reading.\",\n                writing: \"Focus on paragraph structure, linking words, and task response. Practice both formal and informal writing styles with attention to coherence.\",\n                speaking: \"Practice pronunciation, intonation, and natural speech patterns. Work on expressing ideas clearly and developing responses fully.\"\n            },\n            studyPlan: \"Structured study plan focusing on intermediate to upper-intermediate materials. Dedicate 1-2 hours daily with balanced practice across all four skills. Use IELTS preparation materials and general English improvement resources.\",\n            nextSteps: [\n                \"Daily practice with all four skills using varied materials\",\n                \"Join English conversation groups or language exchange programs\",\n                \"Use IELTS preparation books and online resources systematically\",\n                \"Focus on building vocabulary through reading and listening\",\n                \"Take regular practice tests to track improvement\"\n            ]\n        };\n    } else {\n        return {\n            overallAssessment: \"Foundation level with significant room for improvement across all skills. Your overall band score of \" + overallScore + \" indicates limited user level, with basic competence limited to familiar situations and frequent communication breakdowns.\",\n            strengths: [\n                \"Basic understanding of English structure and patterns\",\n                \"Willingness to communicate despite limitations\",\n                \"Some vocabulary knowledge for familiar topics\",\n                \"Ability to convey basic information in simple situations\"\n            ],\n            areasForImprovement: [\n                \"Build fundamental vocabulary for everyday situations\",\n                \"Strengthen basic grammar and sentence construction\",\n                \"Improve listening comprehension for simple conversations\",\n                \"Develop basic writing skills and paragraph organization\"\n            ],\n            specificRecommendations: {\n                listening: \"Start with simple conversations, basic instructions, and familiar topics. Use visual aids and context clues to support understanding.\",\n                reading: \"Begin with short, simple texts on familiar topics. Focus on building sight vocabulary and basic comprehension skills.\",\n                writing: \"Focus on basic sentence structure, simple paragraphs, and essential grammar patterns. Practice writing about familiar topics.\",\n                speaking: \"Practice basic conversations, pronunciation of common words, and expressing simple ideas clearly and confidently.\"\n            },\n            studyPlan: \"Intensive foundation course focusing on basic English skills. Dedicate 1-2 hours daily to structured learning with emphasis on building confidence and fundamental skills. Use beginner-level materials and seek guidance from qualified teachers.\",\n            nextSteps: [\n                \"Enroll in a basic English course with qualified instruction\",\n                \"Practice daily with simple, structured materials\",\n                \"Focus on building confidence through successful communication\",\n                \"Use visual and audio aids to support learning\",\n                \"Set small, achievable goals to maintain motivation\"\n            ]\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/ai-service.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   handlers: () => (/* binding */ handlers),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./db */ \"(rsc)/./src/lib/db/index.ts\");\n/* harmony import */ var _db_schema__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./db/schema */ \"(rsc)/./src/lib/db/schema.ts\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_db__WEBPACK_IMPORTED_MODULE_2__]);\n_db__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nconst { handlers, auth, signIn, signOut } = (0,next_auth__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    session: {\n        strategy: 'jwt'\n    },\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            name: 'credentials',\n            credentials: {\n                email: {\n                    label: 'Email',\n                    type: 'email'\n                },\n                password: {\n                    label: 'Password',\n                    type: 'password'\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    return null;\n                }\n                try {\n                    // Find user in database\n                    const foundUser = await _db__WEBPACK_IMPORTED_MODULE_2__.db.select().from(_db_schema__WEBPACK_IMPORTED_MODULE_3__.users).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_db_schema__WEBPACK_IMPORTED_MODULE_3__.users.email, credentials.email)).limit(1);\n                    if (foundUser.length === 0) {\n                        return null;\n                    }\n                    const user = foundUser[0];\n                    // Check password\n                    if (!user.password) {\n                        return null;\n                    }\n                    const isValidPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_4__[\"default\"].compare(credentials.password, user.password);\n                    if (!isValidPassword) {\n                        return null;\n                    }\n                    return {\n                        id: user.id,\n                        email: user.email,\n                        name: user.name,\n                        role: user.role\n                    };\n                } catch (error) {\n                    console.error('Authentication error:', error);\n                    return null;\n                }\n            }\n        })\n    ],\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.id = user.id;\n                token.role = user.role;\n                token.email = user.email;\n                token.name = user.name;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.id;\n                session.user.role = token.role;\n                session.user.email = token.email;\n                session.user.name = token.name;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: '/auth/signin'\n    }\n});\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/certificate-generator.ts":
/*!******************************************!*\
  !*** ./src/lib/certificate-generator.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   generateCertificate: () => (/* binding */ generateCertificate)\n/* harmony export */ });\n/* harmony import */ var jspdf__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jspdf */ \"(rsc)/./node_modules/jspdf/dist/jspdf.es.min.js\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ \"(rsc)/./src/lib/utils.ts\");\n\n\nasync function generateCertificate(testResult, candidate, registration) {\n    const pdf = new jspdf__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n        orientation: 'portrait',\n        unit: 'mm',\n        format: 'a4'\n    });\n    // Set up the certificate design\n    const pageWidth = pdf.internal.pageSize.getWidth();\n    const pageHeight = pdf.internal.pageSize.getHeight();\n    // ===== FRONT PAGE =====\n    createFrontPage(pdf, testResult, candidate, registration, pageWidth, pageHeight);\n    // ===== BACK PAGE =====\n    pdf.addPage();\n    createBackPage(pdf, testResult, candidate, pageWidth, pageHeight);\n    // Convert to base64 string\n    const pdfBase64 = pdf.output('datauristring');\n    return pdfBase64;\n}\nfunction createFrontPage(pdf, testResult, candidate, registration, pageWidth, pageHeight) {\n    // White background\n    pdf.setFillColor(255, 255, 255);\n    pdf.rect(0, 0, pageWidth, pageHeight, 'F');\n    // Main border\n    pdf.setDrawColor(0, 0, 0);\n    pdf.setLineWidth(2);\n    pdf.rect(8, 8, pageWidth - 16, pageHeight - 16);\n    // Inner border\n    pdf.setLineWidth(0.5);\n    pdf.rect(12, 12, pageWidth - 24, pageHeight - 24);\n    // Header section with IELTS branding\n    pdf.setFillColor(0, 51, 102); // Dark blue background\n    pdf.rect(12, 12, pageWidth - 24, 35, 'F');\n    // Main title - UPDATED (no logo)\n    pdf.setTextColor(255, 255, 255);\n    pdf.setFontSize(20);\n    pdf.setFont('helvetica', 'bold');\n    pdf.text('IELTS MOCK Certificate', pageWidth / 2, 30, {\n        align: 'center'\n    });\n    // Remove the old subtitle and validity text - they are no longer included\n    // Candidate details section\n    pdf.setTextColor(0, 0, 0);\n    pdf.setFontSize(12);\n    pdf.setFont('helvetica', 'bold');\n    pdf.text('CANDIDATE DETAILS', 15, 65);\n    // Create a box for candidate details\n    pdf.setDrawColor(0, 0, 0);\n    pdf.setLineWidth(0.5);\n    pdf.rect(15, 70, pageWidth - 30, 60);\n    // Candidate photo (right side) - ENHANCED LARGER PHOTO\n    const photoX = pageWidth - 55;\n    const photoY = 75;\n    const photoWidth = 35;\n    const photoHeight = 40;\n    // Photo border\n    pdf.setLineWidth(1);\n    pdf.rect(photoX, photoY, photoWidth, photoHeight);\n    // Add candidate photo if available - ENHANCED DEBUGGING AND HANDLING\n    if (candidate.photoData) {\n        try {\n            console.log('Photo data available, length:', candidate.photoData.length);\n            // Prepare photo data with better handling\n            let photoData = candidate.photoData;\n            // Handle different photo data formats\n            if (photoData.startsWith('data:image/')) {\n                // Already has data URL prefix\n                console.log('Photo data has data URL prefix');\n            } else if (photoData.startsWith('/9j/') || photoData.match(/^[A-Za-z0-9+/=]+$/)) {\n                // Looks like base64 data\n                photoData = `data:image/jpeg;base64,${photoData}`;\n                console.log('Added data URL prefix to base64 data');\n            } else {\n                throw new Error('Unrecognized photo data format');\n            }\n            // Add the photo with proper sizing - ENLARGED PHOTO\n            pdf.addImage(photoData, 'JPEG', photoX + 1, photoY + 1, photoWidth - 2, photoHeight - 2);\n            console.log('Photo successfully added to certificate');\n        } catch (error) {\n            console.error('Could not add photo to certificate:', error);\n            console.log('Photo data preview:', candidate.photoData?.substring(0, 100));\n            // Enhanced fallback placeholder\n            pdf.setFillColor(245, 245, 245);\n            pdf.rect(photoX + 1, photoY + 1, photoWidth - 2, photoHeight - 2, 'F');\n            pdf.setFontSize(8);\n            pdf.setTextColor(128, 128, 128);\n            pdf.text('Candidate', photoX + photoWidth / 2, photoY + photoHeight / 2 - 2, {\n                align: 'center'\n            });\n            pdf.text('Photo', photoX + photoWidth / 2, photoY + photoHeight / 2 + 2, {\n                align: 'center'\n            });\n        }\n    } else {\n        console.log('No photo data available for candidate');\n        // Enhanced placeholder if no photo\n        pdf.setFillColor(245, 245, 245);\n        pdf.rect(photoX + 1, photoY + 1, photoWidth - 2, photoHeight - 2, 'F');\n        pdf.setFontSize(8);\n        pdf.setTextColor(128, 128, 128);\n        pdf.text('Candidate', photoX + photoWidth / 2, photoY + photoHeight / 2 - 2, {\n            align: 'center'\n        });\n        pdf.text('Photo', photoX + photoWidth / 2, photoY + photoHeight / 2 + 2, {\n            align: 'center'\n        });\n    }\n    // Candidate information (left side) - IMPROVED LAYOUT\n    pdf.setTextColor(0, 0, 0);\n    pdf.setFontSize(10);\n    pdf.setFont('helvetica', 'normal');\n    const leftCol = 18;\n    const rightCol = 85; // Reduced to prevent overflow\n    pdf.text('Family Name:', leftCol, 85);\n    const familyName = candidate.fullName.split(' ').slice(-1)[0].toUpperCase();\n    pdf.text(familyName.length > 20 ? familyName.substring(0, 20) + '...' : familyName, rightCol, 85);\n    pdf.text('Given Name(s):', leftCol, 92);\n    const givenNames = candidate.fullName.split(' ').slice(0, -1).join(' ').toUpperCase();\n    pdf.text(givenNames.length > 20 ? givenNames.substring(0, 20) + '...' : givenNames, rightCol, 92);\n    pdf.text('Candidate Number:', leftCol, 99);\n    pdf.text(registration?.candidateNumber || 'N/A', rightCol, 99);\n    pdf.text('Date of Birth:', leftCol, 106);\n    pdf.text((0,_utils__WEBPACK_IMPORTED_MODULE_1__.formatDate)(new Date(candidate.dateOfBirth)), rightCol, 106);\n    pdf.text('Identification Type:', leftCol, 113);\n    pdf.text('Passport', rightCol, 113);\n    pdf.text('Identification Number:', leftCol, 120);\n    const passportNum = candidate.passportNumber;\n    pdf.text(passportNum.length > 15 ? passportNum.substring(0, 15) + '...' : passportNum, rightCol, 120);\n    pdf.text('Country/Region of Origin:', leftCol, 127);\n    const nationality = candidate.nationality;\n    pdf.text(nationality.length > 15 ? nationality.substring(0, 15) + '...' : nationality, rightCol, 127);\n    // Test details section\n    pdf.setFontSize(12);\n    pdf.setFont('helvetica', 'bold');\n    pdf.text('TEST DETAILS', 15, 145);\n    // Test details box\n    pdf.setLineWidth(0.5);\n    pdf.rect(15, 150, pageWidth - 30, 25);\n    pdf.setFontSize(10);\n    pdf.setFont('helvetica', 'normal');\n    pdf.text('Test Date:', 18, 160);\n    pdf.text((0,_utils__WEBPACK_IMPORTED_MODULE_1__.formatDate)(new Date(registration?.testDate || new Date())), 85, 160);\n    pdf.text('Test Centre Number:', 18, 167);\n    pdf.text('SAM001', 85, 167); // Samarkand test center code\n    pdf.text('Test Centre Name:', 18, 174);\n    const testCenter = registration?.testCenter || 'Innovative Centre - Samarkand';\n    pdf.text(testCenter.length > 25 ? testCenter.substring(0, 25) + '...' : testCenter, 85, 174);\n    // Test Results section - MOVED HIGHER\n    pdf.setFontSize(12);\n    pdf.setFont('helvetica', 'bold');\n    pdf.text('TEST RESULTS', 15, 180);\n    // Overall Band Score section - MADE MORE COMPACT\n    const overallY = 185;\n    pdf.setFillColor(0, 51, 102);\n    pdf.rect(15, overallY, pageWidth - 30, 20, 'F'); // Reduced height from 25 to 20\n    pdf.setTextColor(255, 255, 255);\n    pdf.setFontSize(18); // Increased font size for prominence\n    pdf.setFont('helvetica', 'bold');\n    // More compact layout - all on one line with better spacing\n    const overallScore = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.formatBandScore)(Number(testResult.overallBandScore) || 0);\n    const cefrLevel = getCEFRLevel(Number(testResult.overallBandScore) || 0);\n    const description = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.getBandScoreDescription)(Number(testResult.overallBandScore) || 0);\n    pdf.text(`OVERALL BAND SCORE ${overallScore} ${cefrLevel} ${description}`, 20, overallY + 13);\n    // Results table - MOVED HIGHER\n    const tableStartY = 215; // Moved up from 230\n    pdf.setLineWidth(0.5);\n    pdf.rect(15, tableStartY, pageWidth - 30, 65);\n    // Table headers\n    pdf.setFillColor(240, 240, 240);\n    pdf.rect(15, tableStartY, pageWidth - 30, 15, 'F');\n    pdf.setTextColor(0, 0, 0);\n    pdf.setFontSize(10);\n    pdf.setFont('helvetica', 'bold');\n    pdf.text('Skill', 20, tableStartY + 10);\n    pdf.text('Band Score', 70, tableStartY + 10);\n    pdf.text('CEFR Level', 110, tableStartY + 10);\n    pdf.text('Description', 140, tableStartY + 10);\n    // Horizontal lines for table structure - IMPROVED SPACING\n    pdf.line(15, tableStartY + 15, pageWidth - 15, tableStartY + 15);\n    pdf.line(65, tableStartY, 65, tableStartY + 65); // Skill column (reduced width)\n    pdf.line(105, tableStartY, 105, tableStartY + 65); // Band Score column (reduced width)\n    pdf.line(135, tableStartY, 135, tableStartY + 65); // CEFR column (reduced width)\n    // Test results data\n    const skills = [\n        {\n            name: 'Listening',\n            band: (0,_utils__WEBPACK_IMPORTED_MODULE_1__.formatBandScore)(Number(testResult.listeningBandScore) || 0),\n            cefr: getCEFRLevel(Number(testResult.listeningBandScore) || 0),\n            description: (0,_utils__WEBPACK_IMPORTED_MODULE_1__.getBandScoreDescription)(Number(testResult.listeningBandScore) || 0)\n        },\n        {\n            name: 'Reading',\n            band: (0,_utils__WEBPACK_IMPORTED_MODULE_1__.formatBandScore)(Number(testResult.readingBandScore) || 0),\n            cefr: getCEFRLevel(Number(testResult.readingBandScore) || 0),\n            description: (0,_utils__WEBPACK_IMPORTED_MODULE_1__.getBandScoreDescription)(Number(testResult.readingBandScore) || 0)\n        },\n        {\n            name: 'Writing',\n            band: (0,_utils__WEBPACK_IMPORTED_MODULE_1__.formatBandScore)(Number(testResult.writingBandScore) || 0),\n            cefr: getCEFRLevel(Number(testResult.writingBandScore) || 0),\n            description: (0,_utils__WEBPACK_IMPORTED_MODULE_1__.getBandScoreDescription)(Number(testResult.writingBandScore) || 0)\n        },\n        {\n            name: 'Speaking',\n            band: (0,_utils__WEBPACK_IMPORTED_MODULE_1__.formatBandScore)(Number(testResult.speakingBandScore) || 0),\n            cefr: getCEFRLevel(Number(testResult.speakingBandScore) || 0),\n            description: (0,_utils__WEBPACK_IMPORTED_MODULE_1__.getBandScoreDescription)(Number(testResult.speakingBandScore) || 0)\n        }\n    ];\n    pdf.setFont('helvetica', 'normal');\n    skills.forEach((skill, index)=>{\n        const rowY = tableStartY + 25 + index * 12;\n        // Horizontal line between rows\n        if (index > 0) {\n            pdf.line(15, rowY - 6, pageWidth - 15, rowY - 6);\n        }\n        pdf.text(skill.name, 20, rowY);\n        pdf.text(skill.band, 75, rowY);\n        pdf.text(skill.cefr, 115, rowY);\n        // Improved description handling - shorter text to prevent overflow\n        const description = skill.description;\n        if (description.length > 15) {\n            pdf.text(description.substring(0, 15) + '...', 145, rowY);\n        } else {\n            pdf.text(description, 145, rowY);\n        }\n    });\n}\nfunction createBackPage(pdf, testResult, candidate, pageWidth, pageHeight) {\n    // White background\n    pdf.setFillColor(255, 255, 255);\n    pdf.rect(0, 0, pageWidth, pageHeight, 'F');\n    // Main border\n    pdf.setDrawColor(0, 0, 0);\n    pdf.setLineWidth(2);\n    pdf.rect(8, 8, pageWidth - 16, pageHeight - 16);\n    // Inner border\n    pdf.setLineWidth(0.5);\n    pdf.rect(12, 12, pageWidth - 24, pageHeight - 24);\n    // Header section\n    pdf.setFillColor(0, 51, 102);\n    pdf.rect(12, 12, pageWidth - 24, 35, 'F');\n    pdf.setTextColor(255, 255, 255);\n    pdf.setFontSize(18);\n    pdf.setFont('helvetica', 'bold');\n    pdf.text('IELTS MOCK Certificate', pageWidth / 2, 25, {\n        align: 'center'\n    });\n    pdf.setFontSize(12);\n    pdf.setFont('helvetica', 'normal');\n    pdf.text('Additional Information', pageWidth / 2, 35, {\n        align: 'center'\n    });\n    // Authentication section\n    pdf.setTextColor(0, 0, 0);\n    pdf.setFontSize(12);\n    pdf.setFont('helvetica', 'bold');\n    pdf.text('AUTHENTICATION', 15, 65);\n    pdf.setLineWidth(0.5);\n    pdf.rect(15, 70, pageWidth - 30, 40);\n    pdf.setFontSize(10);\n    pdf.setFont('helvetica', 'normal');\n    pdf.text('Centre Stamp:', 20, 85);\n    pdf.text('Authorised Signature:', 20, 100);\n    // Certificate serial and verification info\n    pdf.text(`Certificate Serial: ${testResult.certificateSerial || 'N/A'}`, 120, 85);\n    pdf.text(`Issue Date: ${(0,_utils__WEBPACK_IMPORTED_MODULE_1__.formatDate)(new Date())}`, 120, 100);\n    // Band Score Descriptions\n    pdf.setFontSize(12);\n    pdf.setFont('helvetica', 'bold');\n    pdf.text('BAND SCORE DESCRIPTIONS', 15, 130);\n    pdf.setLineWidth(0.5);\n    pdf.rect(15, 135, pageWidth - 30, 120);\n    pdf.setFontSize(9);\n    pdf.setFont('helvetica', 'normal');\n    const descriptions = [\n        'Band 9: Expert User - Has fully operational command of the language',\n        'Band 8: Very Good User - Has fully operational command with occasional inaccuracies',\n        'Band 7: Good User - Has operational command with occasional inaccuracies',\n        'Band 6: Competent User - Has generally effective command despite inaccuracies',\n        'Band 5: Modest User - Has partial command with frequent problems',\n        'Band 4: Limited User - Basic competence limited to familiar situations',\n        'Band 3: Extremely Limited User - Conveys general meaning in familiar situations',\n        'Band 2: Intermittent User - No real communication except basic information',\n        'Band 1: Non User - No ability to use the language'\n    ];\n    descriptions.forEach((desc, index)=>{\n        pdf.text(desc, 20, 145 + index * 12);\n    });\n// Important Notes section removed as requested\n}\n// Helper function to get CEFR level from band score\nfunction getCEFRLevel(bandScore) {\n    if (bandScore >= 8.5) return 'C2';\n    if (bandScore >= 7.0) return 'C1';\n    if (bandScore >= 5.5) return 'B2';\n    if (bandScore >= 4.0) return 'B1';\n    if (bandScore >= 3.0) return 'A2';\n    return 'A1';\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/certificate-generator.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/index.ts":
/*!*****************************!*\
  !*** ./src/lib/db/index.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   accounts: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.accounts),\n/* harmony export */   aiFeedback: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.aiFeedback),\n/* harmony export */   candidates: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.candidates),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   sessions: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.sessions),\n/* harmony export */   testRegistrations: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.testRegistrations),\n/* harmony export */   testResults: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.testResults),\n/* harmony export */   users: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.users),\n/* harmony export */   verificationTokens: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.verificationTokens)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! drizzle-orm/postgres-js */ \"(rsc)/./node_modules/drizzle-orm/postgres-js/driver.js\");\n/* harmony import */ var postgres__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! postgres */ \"postgres\");\n/* harmony import */ var _schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./schema */ \"(rsc)/./src/lib/db/schema.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([postgres__WEBPACK_IMPORTED_MODULE_0__, drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__]);\n([postgres__WEBPACK_IMPORTED_MODULE_0__, drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nconst connectionString = process.env.DATABASE_URL;\n// Disable prefetch as it is not supported for \"Transaction\" pool mode\nconst client = (0,postgres__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(connectionString, {\n    prepare: false\n});\nconst db = (0,drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__.drizzle)(client, {\n    schema: _schema__WEBPACK_IMPORTED_MODULE_1__\n});\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFrRDtBQUNsQjtBQUNHO0FBRW5DLE1BQU1HLG1CQUFtQkMsUUFBUUMsR0FBRyxDQUFDQyxZQUFZO0FBRWpELHNFQUFzRTtBQUN0RSxNQUFNQyxTQUFTTixvREFBUUEsQ0FBQ0Usa0JBQWtCO0lBQUVLLFNBQVM7QUFBTTtBQUNwRCxNQUFNQyxLQUFLVCxnRUFBT0EsQ0FBQ08sUUFBUTtJQUFFTCxNQUFNQSxzQ0FBQUE7QUFBQyxHQUFHO0FBRXJCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFdpbmRvd3MgMTFcXERlc2t0b3BcXGNvZGVzXFxJRUxUUy1DZXJ0aWZpY2F0aW9uLVN5c3RlbVxcc3JjXFxsaWJcXGRiXFxpbmRleC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBkcml6emxlIH0gZnJvbSAnZHJpenpsZS1vcm0vcG9zdGdyZXMtanMnO1xuaW1wb3J0IHBvc3RncmVzIGZyb20gJ3Bvc3RncmVzJztcbmltcG9ydCAqIGFzIHNjaGVtYSBmcm9tICcuL3NjaGVtYSc7XG5cbmNvbnN0IGNvbm5lY3Rpb25TdHJpbmcgPSBwcm9jZXNzLmVudi5EQVRBQkFTRV9VUkwhO1xuXG4vLyBEaXNhYmxlIHByZWZldGNoIGFzIGl0IGlzIG5vdCBzdXBwb3J0ZWQgZm9yIFwiVHJhbnNhY3Rpb25cIiBwb29sIG1vZGVcbmNvbnN0IGNsaWVudCA9IHBvc3RncmVzKGNvbm5lY3Rpb25TdHJpbmcsIHsgcHJlcGFyZTogZmFsc2UgfSk7XG5leHBvcnQgY29uc3QgZGIgPSBkcml6emxlKGNsaWVudCwgeyBzY2hlbWEgfSk7XG5cbmV4cG9ydCAqIGZyb20gJy4vc2NoZW1hJztcbiJdLCJuYW1lcyI6WyJkcml6emxlIiwicG9zdGdyZXMiLCJzY2hlbWEiLCJjb25uZWN0aW9uU3RyaW5nIiwicHJvY2VzcyIsImVudiIsIkRBVEFCQVNFX1VSTCIsImNsaWVudCIsInByZXBhcmUiLCJkYiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/schema.ts":
/*!******************************!*\
  !*** ./src/lib/db/schema.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   accounts: () => (/* binding */ accounts),\n/* harmony export */   aiFeedback: () => (/* binding */ aiFeedback),\n/* harmony export */   candidates: () => (/* binding */ candidates),\n/* harmony export */   sessions: () => (/* binding */ sessions),\n/* harmony export */   testRegistrations: () => (/* binding */ testRegistrations),\n/* harmony export */   testResults: () => (/* binding */ testResults),\n/* harmony export */   users: () => (/* binding */ users),\n/* harmony export */   verificationTokens: () => (/* binding */ verificationTokens)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/table.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/text.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/timestamp.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/integer.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/unique-constraint.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/numeric.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/boolean.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/json.js\");\n/* harmony import */ var _paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @paralleldrive/cuid2 */ \"(rsc)/./node_modules/@paralleldrive/cuid2/index.js\");\n\n\n// Users table for authentication\nconst users = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('users', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    name: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('name'),\n    email: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('email').notNull().unique(),\n    emailVerified: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('emailVerified', {\n        mode: 'date'\n    }),\n    image: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('image'),\n    password: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('password'),\n    role: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('role', {\n        enum: [\n            'admin',\n            'test_checker'\n        ]\n    }).notNull().default('test_checker'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('updated_at').defaultNow().notNull()\n});\n// Accounts table for OAuth\nconst accounts = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('accounts', {\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('userId').notNull().references(()=>users.id, {\n        onDelete: 'cascade'\n    }),\n    type: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('type').notNull(),\n    provider: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('provider').notNull(),\n    providerAccountId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('providerAccountId').notNull(),\n    refresh_token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('refresh_token'),\n    access_token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('access_token'),\n    expires_at: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.integer)('expires_at'),\n    token_type: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('token_type'),\n    scope: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('scope'),\n    id_token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id_token'),\n    session_state: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('session_state')\n});\n// Sessions table for authentication\nconst sessions = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('sessions', {\n    sessionToken: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('sessionToken').primaryKey(),\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('userId').notNull().references(()=>users.id, {\n        onDelete: 'cascade'\n    }),\n    expires: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('expires', {\n        mode: 'date'\n    }).notNull()\n});\n// Verification tokens\nconst verificationTokens = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('verificationTokens', {\n    identifier: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('identifier').notNull(),\n    token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('token').notNull(),\n    expires: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('expires', {\n        mode: 'date'\n    }).notNull()\n});\n// Candidates table - Core candidate profile information\nconst candidates = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('candidates', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    fullName: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('full_name').notNull(),\n    email: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('email').notNull(),\n    phoneNumber: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('phone_number').notNull(),\n    dateOfBirth: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('date_of_birth', {\n        mode: 'date'\n    }).notNull(),\n    nationality: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('nationality').notNull(),\n    passportNumber: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('passport_number').notNull().unique(),\n    photoUrl: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('photo_url'),\n    photoData: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('photo_data'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('updated_at').defaultNow().notNull()\n});\n// Test registrations table - Individual test registrations for candidates\nconst testRegistrations = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('test_registrations', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    candidateId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('candidate_id').notNull().references(()=>candidates.id, {\n        onDelete: 'cascade'\n    }),\n    candidateNumber: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('candidate_number').notNull(),\n    testDate: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('test_date', {\n        mode: 'date'\n    }).notNull(),\n    testCenter: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('test_center').notNull(),\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('status', {\n        enum: [\n            'registered',\n            'completed',\n            'cancelled'\n        ]\n    }).notNull().default('registered'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('updated_at').defaultNow().notNull()\n}, (table)=>({\n        // Composite unique constraint: same candidate cannot register for the same test date\n        uniqueCandidateTestDate: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.unique)().on(table.candidateId, table.testDate),\n        // Candidate number should be unique only within each test date\n        uniqueCandidateNumberTestDate: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.unique)().on(table.candidateNumber, table.testDate)\n    }));\n// Test results table\nconst testResults = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('test_results', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    testRegistrationId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('test_registration_id').notNull().references(()=>testRegistrations.id, {\n        onDelete: 'cascade'\n    }),\n    // Listening scores\n    listeningScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('listening_score', {\n        precision: 3,\n        scale: 1\n    }),\n    listeningBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('listening_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Reading scores\n    readingScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('reading_score', {\n        precision: 3,\n        scale: 1\n    }),\n    readingBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('reading_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Writing scores\n    writingTask1Score: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('writing_task1_score', {\n        precision: 2,\n        scale: 1\n    }),\n    writingTask2Score: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('writing_task2_score', {\n        precision: 2,\n        scale: 1\n    }),\n    writingBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('writing_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Speaking scores\n    speakingFluencyScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('speaking_fluency_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingLexicalScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('speaking_lexical_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingGrammarScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('speaking_grammar_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingPronunciationScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('speaking_pronunciation_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('speaking_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Overall score\n    overallBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.decimal)('overall_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Status and metadata\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('status', {\n        enum: [\n            'pending',\n            'completed',\n            'verified'\n        ]\n    }).notNull().default('pending'),\n    enteredBy: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('entered_by').references(()=>users.id),\n    verifiedBy: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('verified_by').references(()=>users.id),\n    certificateGenerated: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.boolean)('certificate_generated').default(false),\n    certificateSerial: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('certificate_serial').unique(),\n    certificateUrl: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('certificate_url'),\n    aiFeedbackGenerated: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.boolean)('ai_feedback_generated').default(false),\n    testDate: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('test_date', {\n        mode: 'date'\n    }),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('updated_at').defaultNow().notNull()\n});\n// AI feedback table\nconst aiFeedback = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('ai_feedback', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    testResultId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('test_result_id').notNull().references(()=>testResults.id, {\n        onDelete: 'cascade'\n    }),\n    // Feedback for each skill\n    listeningFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('listening_feedback'),\n    readingFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('reading_feedback'),\n    writingFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('writing_feedback'),\n    speakingFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('speaking_feedback'),\n    // Overall feedback and recommendations\n    overallFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('overall_feedback'),\n    studyRecommendations: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('study_recommendations'),\n    // Strengths and weaknesses\n    strengths: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_8__.json)('strengths').$type(),\n    weaknesses: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_8__.json)('weaknesses').$type(),\n    // Study plan suggestions\n    studyPlan: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_8__.json)('study_plan').$type(),\n    generatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('generated_at').defaultNow().notNull()\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/schema.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateOverallBandScore: () => (/* binding */ calculateOverallBandScore),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   formatBandScore: () => (/* binding */ formatBandScore),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   getBandScoreDescription: () => (/* binding */ getBandScoreDescription),\n/* harmony export */   getListeningBandScore: () => (/* binding */ getListeningBandScore),\n/* harmony export */   getReadingBandScore: () => (/* binding */ getReadingBandScore)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(rsc)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(rsc)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n// IELTS band score calculation utilities\nfunction calculateOverallBandScore(listening, reading, writing, speaking) {\n    const average = (listening + reading + writing + speaking) / 4;\n    // Round to nearest 0.5\n    return Math.round(average * 2) / 2;\n}\nfunction getListeningBandScore(rawScore) {\n    // IELTS Listening band score conversion (out of 40)\n    const conversionTable = {\n        39: 9.0,\n        38: 9.0,\n        37: 8.5,\n        36: 8.5,\n        35: 8.0,\n        34: 8.0,\n        33: 7.5,\n        32: 7.5,\n        31: 7.0,\n        30: 7.0,\n        29: 6.5,\n        28: 6.5,\n        27: 6.0,\n        26: 6.0,\n        25: 5.5,\n        24: 5.5,\n        23: 5.0,\n        22: 5.0,\n        21: 4.5,\n        20: 4.5,\n        19: 4.0,\n        18: 4.0,\n        17: 3.5,\n        16: 3.5,\n        15: 3.0,\n        14: 3.0,\n        13: 2.5,\n        12: 2.5,\n        11: 2.0,\n        10: 2.0,\n        9: 1.5,\n        8: 1.5,\n        7: 1.0,\n        6: 1.0,\n        5: 0.5,\n        4: 0.5,\n        3: 0.0,\n        2: 0.0,\n        1: 0.0,\n        0: 0.0\n    };\n    return conversionTable[Math.floor(rawScore)] || 0.0;\n}\nfunction getReadingBandScore(rawScore) {\n    // IELTS Reading band score conversion (out of 40)\n    const conversionTable = {\n        39: 9.0,\n        38: 9.0,\n        37: 8.5,\n        36: 8.5,\n        35: 8.0,\n        34: 8.0,\n        33: 7.5,\n        32: 7.5,\n        31: 7.0,\n        30: 7.0,\n        29: 6.5,\n        28: 6.5,\n        27: 6.0,\n        26: 6.0,\n        25: 5.5,\n        24: 5.5,\n        23: 5.0,\n        22: 5.0,\n        21: 4.5,\n        20: 4.5,\n        19: 4.0,\n        18: 4.0,\n        17: 3.5,\n        16: 3.5,\n        15: 3.0,\n        14: 3.0,\n        13: 2.5,\n        12: 2.5,\n        11: 2.0,\n        10: 2.0,\n        9: 1.5,\n        8: 1.5,\n        7: 1.0,\n        6: 1.0,\n        5: 0.5,\n        4: 0.5,\n        3: 0.0,\n        2: 0.0,\n        1: 0.0,\n        0: 0.0\n    };\n    return conversionTable[Math.floor(rawScore)] || 0.0;\n}\nfunction formatDate(date) {\n    return new Intl.DateTimeFormat('en-US', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric'\n    }).format(date);\n}\nfunction formatBandScore(score) {\n    return score.toFixed(1);\n}\nfunction getBandScoreDescription(score) {\n    if (score >= 9.0) return 'Expert User';\n    if (score >= 8.0) return 'Very Good User';\n    if (score >= 7.0) return 'Good User';\n    if (score >= 6.0) return 'Competent User';\n    if (score >= 5.0) return 'Modest User';\n    if (score >= 4.0) return 'Limited User';\n    if (score >= 3.0) return 'Extremely Limited User';\n    if (score >= 2.0) return 'Intermittent User';\n    if (score >= 1.0) return 'Non User';\n    return 'Did not attempt the test';\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/utils/certificate.ts":
/*!**************************************!*\
  !*** ./src/lib/utils/certificate.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   extractYearFromSerial: () => (/* binding */ extractYearFromSerial),\n/* harmony export */   formatCertificateSerial: () => (/* binding */ formatCertificateSerial),\n/* harmony export */   generateCertificateSerial: () => (/* binding */ generateCertificateSerial),\n/* harmony export */   validateCertificateSerial: () => (/* binding */ validateCertificateSerial)\n/* harmony export */ });\n/**\n * Certificate utility functions for IELTS certification system\n */ /**\n * Generate a unique certificate serial number\n * Format: IELTS-YYYY-NNNNNN (e.g., IELTS-2024-123456)\n */ function generateCertificateSerial() {\n    const year = new Date().getFullYear();\n    const randomNumber = Math.floor(Math.random() * 900000) + 100000; // 6-digit number\n    return `IELTS-${year}-${randomNumber}`;\n}\n/**\n * Validate certificate serial number format\n */ function validateCertificateSerial(serial) {\n    const pattern = /^IELTS-\\d{4}-\\d{6}$/;\n    return pattern.test(serial);\n}\n/**\n * Extract year from certificate serial\n */ function extractYearFromSerial(serial) {\n    const match = serial.match(/^IELTS-(\\d{4})-\\d{6}$/);\n    return match ? parseInt(match[1], 10) : null;\n}\n/**\n * Format certificate serial for display\n */ function formatCertificateSerial(serial) {\n    if (!validateCertificateSerial(serial)) {\n        return serial;\n    }\n    // Add spaces for better readability: IELTS-2024-123456 -> IELTS 2024 123456\n    return serial.replace(/-/g, ' ');\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils/certificate.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@auth","vendor-chunks/next-auth","vendor-chunks/drizzle-orm","vendor-chunks/@noble","vendor-chunks/@paralleldrive","vendor-chunks/oauth4webapi","vendor-chunks/jose","vendor-chunks/bcryptjs","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/@panva","vendor-chunks/@babel","vendor-chunks/@anthropic-ai","vendor-chunks/tailwind-merge","vendor-chunks/fflate","vendor-chunks/clsx","vendor-chunks/jspdf"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchecker%2Fresults%2Froute&page=%2Fapi%2Fchecker%2Fresults%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchecker%2Fresults%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();