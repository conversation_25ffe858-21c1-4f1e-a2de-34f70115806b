# Enhanced IELTS System - Deployment & Configuration Guide

## 🚀 Deployment Architecture

### Production Environment Stack
```yaml
Platform: Vercel (Next.js optimized)
Database: Neon PostgreSQL (Serverless)
CDN: Vercel Edge Network
Monitoring: Vercel Analytics + Custom monitoring
Backup: Automated daily backups
SSL: Automatic HTTPS with Vercel
```

### Environment Configuration
```bash
# Core Application
NEXTAUTH_SECRET="your-production-secret-key-256-bit"
NEXTAUTH_URL="https://your-domain.vercel.app"
DATABASE_URL="********************************/db?sslmode=require"

# Master Admin (Hardcoded Credentials)
MASTER_ADMIN_EMAIL="<EMAIL>"
MASTER_ADMIN_PASSWORD="MasterAdmin2024!@#$"

# AI Integration
ANTHROPIC_API_KEY="sk-ant-api03-your-claude-api-key"

# Payment Gateways
CLICK_SERVICE_ID="your-click-service-id"
CLICK_SECRET_KEY="your-click-secret-key"
CLICK_MERCHANT_ID="your-click-merchant-id"

PAYME_MERCHANT_ID="your-payme-merchant-id"
PAYME_SECRET_KEY="your-payme-secret-key"
PAYME_TEST_MODE="false"

# Email Service (for notifications)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-email-password"

# File Storage
UPLOAD_MAX_SIZE="5242880" # 5MB in bytes
ALLOWED_FILE_TYPES="image/jpeg,image/png,image/webp"

# Security
ENCRYPTION_KEY="your-32-character-encryption-key"
JWT_SECRET="your-jwt-secret-key"
RATE_LIMIT_MAX="100" # requests per minute

# Feature Flags
ENABLE_PAYMENT_PROCESSING="true"
ENABLE_AI_FEEDBACK="true"
ENABLE_CERTIFICATE_GENERATION="true"
ENABLE_PROMOTIONAL_SYSTEM="true"

# Monitoring & Analytics
SENTRY_DSN="your-sentry-dsn"
GOOGLE_ANALYTICS_ID="GA-XXXXXXXXX"
```

---

## 🗄️ Database Setup & Migration

### Initial Database Setup
```sql
-- Create database and user
CREATE DATABASE enhanced_ielts_system;
CREATE USER ielts_admin WITH ENCRYPTED PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE enhanced_ielts_system TO ielts_admin;

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm"; -- for text search
CREATE EXTENSION IF NOT EXISTS "btree_gin"; -- for indexing
```

### Migration Scripts
```bash
# Generate migration files
npm run db:generate

# Apply migrations
npm run db:migrate

# Seed initial data
npm run db:seed

# Create master admin
npm run setup:master-admin

# Setup default promotional rules
npm run setup:promotions
```

### Database Indexes for Performance
```sql
-- Candidate search optimization
CREATE INDEX idx_candidates_passport_org ON candidates(organization_id, passport_number);
CREATE INDEX idx_candidates_email_org ON candidates(organization_id, email);

-- Payment transaction optimization
CREATE INDEX idx_payments_candidate ON payment_transactions(candidate_id, status);
CREATE INDEX idx_payments_gateway ON payment_transactions(gateway, status, created_at);

-- Test results optimization
CREATE INDEX idx_results_candidate ON test_results(candidate_id, test_date DESC);
CREATE INDEX idx_results_org_date ON test_results(organization_id, test_date DESC);

-- Access permissions optimization
CREATE INDEX idx_permissions_candidate_feature ON access_permissions(candidate_id, feature_type, expires_at);

-- Certificate lifecycle optimization
CREATE INDEX idx_certificates_expiration ON certificate_lifecycle(expires_at, status);
```

---

## 🔧 Application Configuration

### Next.js Configuration
```typescript
// next.config.ts
import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Server configuration
  serverExternalPackages: ['postgres', 'bcryptjs'],
  
  // Image optimization
  images: {
    domains: ['localhost', 'your-domain.vercel.app'],
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 60,
  },
  
  // Security headers
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
        ],
      },
    ];
  },
  
  // Redirects for SEO
  async redirects() {
    return [
      {
        source: '/admin',
        destination: '/admin/dashboard',
        permanent: true,
      },
    ];
  },
  
  // Experimental features
  experimental: {
    serverComponentsExternalPackages: ['postgres'],
  },
};

export default nextConfig;
```

### Drizzle Configuration
```typescript
// drizzle.config.ts
import { defineConfig } from 'drizzle-kit';

export default defineConfig({
  schema: './src/lib/db/schema.ts',
  out: './drizzle',
  dialect: 'postgresql',
  dbCredentials: {
    url: process.env.DATABASE_URL!,
  },
  verbose: true,
  strict: true,
  migrations: {
    prefix: 'timestamp',
    table: 'drizzle_migrations',
    schema: 'public',
  },
});
```

---

## 📊 Monitoring & Analytics Setup

### Application Monitoring
```typescript
// lib/monitoring.ts
import * as Sentry from '@sentry/nextjs';

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  environment: process.env.NODE_ENV,
  tracesSampleRate: 1.0,
  beforeSend(event) {
    // Filter sensitive data
    if (event.request?.headers) {
      delete event.request.headers.authorization;
      delete event.request.headers.cookie;
    }
    return event;
  },
});

// Custom metrics tracking
export const trackPaymentEvent = (event: string, data: any) => {
  Sentry.addBreadcrumb({
    category: 'payment',
    message: event,
    data,
    level: 'info',
  });
};
```

### Performance Monitoring
```typescript
// lib/performance.ts
export const performanceConfig = {
  // Core Web Vitals thresholds
  LCP_THRESHOLD: 2500, // Largest Contentful Paint
  FID_THRESHOLD: 100,  // First Input Delay
  CLS_THRESHOLD: 0.1,  // Cumulative Layout Shift
  
  // Custom metrics
  API_RESPONSE_THRESHOLD: 1000,
  DATABASE_QUERY_THRESHOLD: 500,
  PAYMENT_PROCESSING_THRESHOLD: 5000,
};

export const trackPerformance = (metric: string, value: number) => {
  if (typeof window !== 'undefined') {
    // Client-side tracking
    window.gtag?.('event', 'timing_complete', {
      name: metric,
      value: value,
    });
  }
};
```

---

## 🔐 Security Configuration

### Rate Limiting
```typescript
// lib/rate-limit.ts
import { Ratelimit } from '@upstash/ratelimit';
import { Redis } from '@upstash/redis';

const redis = new Redis({
  url: process.env.UPSTASH_REDIS_REST_URL!,
  token: process.env.UPSTASH_REDIS_REST_TOKEN!,
});

export const rateLimiter = new Ratelimit({
  redis,
  limiter: Ratelimit.slidingWindow(100, '1 m'), // 100 requests per minute
  analytics: true,
});

export const paymentRateLimiter = new Ratelimit({
  redis,
  limiter: Ratelimit.slidingWindow(5, '1 m'), // 5 payment attempts per minute
  analytics: true,
});
```

### Data Encryption
```typescript
// lib/encryption.ts
import crypto from 'crypto';

const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY!;
const ALGORITHM = 'aes-256-gcm';

export const encrypt = (text: string): string => {
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipher(ALGORITHM, ENCRYPTION_KEY);
  cipher.setAAD(Buffer.from('ielts-system'));
  
  let encrypted = cipher.update(text, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  
  const authTag = cipher.getAuthTag();
  return `${iv.toString('hex')}:${authTag.toString('hex')}:${encrypted}`;
};

export const decrypt = (encryptedText: string): string => {
  const [ivHex, authTagHex, encrypted] = encryptedText.split(':');
  const iv = Buffer.from(ivHex, 'hex');
  const authTag = Buffer.from(authTagHex, 'hex');
  
  const decipher = crypto.createDecipher(ALGORITHM, ENCRYPTION_KEY);
  decipher.setAAD(Buffer.from('ielts-system'));
  decipher.setAuthTag(authTag);
  
  let decrypted = decipher.update(encrypted, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  
  return decrypted;
};
```

---

## 🔄 Scheduled Jobs Configuration

### Cron Jobs Setup
```typescript
// lib/cron-jobs.ts
import cron from 'node-cron';

// Certificate expiration check (daily at 2 AM)
cron.schedule('0 2 * * *', async () => {
  console.log('Running certificate expiration check...');
  await processCertificateExpiration();
});

// Payment verification (every 15 minutes)
cron.schedule('*/15 * * * *', async () => {
  console.log('Verifying pending payments...');
  await verifyPendingPayments();
});

// Analytics generation (daily at 3 AM)
cron.schedule('0 3 * * *', async () => {
  console.log('Generating daily analytics...');
  await generateDailyAnalytics();
});

// Database cleanup (weekly on Sunday at 4 AM)
cron.schedule('0 4 * * 0', async () => {
  console.log('Running database cleanup...');
  await cleanupExpiredData();
});
```

---

## 📧 Email Configuration

### SMTP Setup
```typescript
// lib/email.ts
import nodemailer from 'nodemailer';

const transporter = nodemailer.createTransporter({
  host: process.env.SMTP_HOST,
  port: parseInt(process.env.SMTP_PORT || '587'),
  secure: false,
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS,
  },
});

export const sendCertificateExpirationWarning = async (
  email: string,
  candidateName: string,
  expirationDate: string
) => {
  await transporter.sendMail({
    from: process.env.SMTP_USER,
    to: email,
    subject: 'IELTS Certificate Expiration Warning',
    html: `
      <h2>Certificate Expiration Notice</h2>
      <p>Dear ${candidateName},</p>
      <p>Your IELTS certificate will expire on ${expirationDate}.</p>
      <p>Please download your certificate before it expires.</p>
    `,
  });
};
```

---

## 🚀 Deployment Scripts

### Production Deployment
```bash
#!/bin/bash
# deploy.sh

echo "Starting Enhanced IELTS System deployment..."

# Build the application
npm run build

# Run database migrations
npm run db:migrate

# Verify environment variables
npm run verify:env

# Deploy to Vercel
vercel --prod

# Run post-deployment checks
npm run health:check

echo "Deployment completed successfully!"
```

### Health Check Script
```typescript
// scripts/health-check.ts
import { db } from '../src/lib/db';

async function healthCheck() {
  try {
    // Database connectivity
    await db.execute('SELECT 1');
    console.log('✅ Database connection: OK');
    
    // Payment gateway connectivity
    const clickHealth = await checkClickAPI();
    const paymeHealth = await checkPaymeAPI();
    console.log(`✅ Click API: ${clickHealth ? 'OK' : 'FAILED'}`);
    console.log(`✅ Payme API: ${paymeHealth ? 'OK' : 'FAILED'}`);
    
    // AI service connectivity
    const aiHealth = await checkAnthropicAPI();
    console.log(`✅ Anthropic API: ${aiHealth ? 'OK' : 'FAILED'}`);
    
    console.log('🎉 All systems operational!');
  } catch (error) {
    console.error('❌ Health check failed:', error);
    process.exit(1);
  }
}

healthCheck();
```

This deployment configuration provides comprehensive setup instructions for the enhanced IELTS system with all security, monitoring, and operational requirements.
