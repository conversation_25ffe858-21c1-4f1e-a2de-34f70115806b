# Enhanced IELTS Certification System - Complete Implementation Plan

## Project Overview

The Enhanced IELTS Certification System is a multi-organization platform with paywall-protected premium features, comprehensive candidate management, and flexible promotional systems. This system supports multiple test centers under a master organization structure with integrated payment processing.

**Purpose**: Create a scalable, monetized IELTS test management platform with premium features, multi-organization support, and comprehensive candidate progress tracking.

## 2. Technology Stack

### Frontend
- **Framework**: Next.js 15 (App Router)
- **UI Library**: React 19
- **Styling**: Tailwind CSS
- **Icons**: Lucide React
- **Components**: Radix UI primitives
- **Forms**: React Hook Form with Zod validation
- **State Management**: React hooks and context

### Backend
- **Runtime**: Node.js
- **Framework**: Next.js API Routes
- **Database ORM**: Drizzle ORM
- **Database**: PostgreSQL (Neon)
- **Authentication**: NextAuth.js v5
- **Password Hashing**: bcryptjs

### External Services
- **AI Integration**: Anthropic Claude API (Claude 3 Sonnet)
- **PDF Generation**: jsPDF with html2canvas
- **Database Provider**: Neon PostgreSQL

### Development Tools
- **Language**: TypeScript
- **Linting**: ESLint
- **Package Manager**: npm
- **Database Migrations**: Drizzle Kit

## 3. Database Schema

### Core Tables

#### Users (`users`)
- Authentication and role management
- Roles: `admin`, `test_checker`
- Password-based authentication with bcrypt

#### Candidates (`candidates`)
- Core candidate profile information
- Unique passport/birth certificate numbers
- Photo storage (base64 encoded)
- One-to-many relationship with test registrations

#### Test Registrations (`test_registrations`)
- Individual test registrations per candidate
- Candidate numbers (001, 002 format per test date)
- Unique constraints: candidate+testDate, candidateNumber+testDate
- Status tracking: registered, completed, cancelled

#### Test Results (`test_results`)
- Comprehensive IELTS scoring system
- Band scores for all four skills (Listening, Reading, Writing, Speaking)
- Detailed component scores for Speaking
- Certificate generation tracking
- AI feedback generation flags

#### AI Feedback (`ai_feedback`)
- Skill-specific feedback for each module
- Overall assessment and recommendations
- Study plans and improvement suggestions
- Strengths and weaknesses analysis

### Key Relationships
- Candidates → Test Registrations (1:many)
- Test Registrations → Test Results (1:1)
- Test Results → AI Feedback (1:1)

## 4. Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── admin/             # Admin dashboard pages
│   │   ├── candidates/    # Candidate management
│   │   ├── results/       # Results management
│   │   ├── reports/       # System reports
│   │   └── search/        # Admin search
│   ├── dashboard/         # Test checker interface
│   │   ├── results/       # Result entry and management
│   │   ├── feedback/      # AI feedback generation
│   │   └── search/        # Checker search
│   ├── results/[id]/      # Public results pages
│   ├── search/            # Public search page
│   ├── verify/[serial]/   # Certificate verification
│   ├── auth/signin/       # Authentication
│   └── api/               # API routes
├── lib/                   # Core utilities
│   ├── db/               # Database configuration
│   ├── auth.ts           # NextAuth configuration
│   ├── ai-service.ts     # Anthropic integration
│   ├── certificate-generator.ts # PDF generation
│   ├── utils/            # Utility functions
│   └── validations.ts    # Zod schemas
├── components/           # Reusable components
├── middleware.ts         # Route protection
└── types/               # TypeScript definitions
```

## 5. Core Features

### Authentication & Authorization
- **NextAuth.js v5** with credentials provider
- **Role-based access control**: Admin and Test Checker roles
- **Route protection** via middleware
- **Session management** with JWT strategy

### Candidate Management
- **Unified identification**: Single field for passport/birth certificate numbers
- **Photo storage**: Base64 encoding in PostgreSQL
- **Duplicate prevention**: Existing candidate detection during registration
- **Multi-test registration**: Candidates can register for multiple test dates

### Test Result Management
- **Comprehensive scoring**: All IELTS modules with band calculations
- **Quick entry interface**: Table-based design for fast data entry
- **Status tracking**: Pending → Completed → Verified workflow
- **Automatic processing**: AI feedback and certificate generation on completion

### AI Feedback System
- **Anthropic Claude integration**: Claude 3 Sonnet model
- **Comprehensive analysis**: Module-specific and overall feedback
- **Study recommendations**: Personalized improvement plans
- **Automatic generation**: Triggered when results are completed
- **Public access**: Available on public results pages

### Certificate Generation
- **Professional PDF certificates**: Two-page design with candidate photos
- **Unique serial numbers**: IELTS-YYYY-NNNNNN format
- **Security features**: Serial verification system
- **Automatic generation**: Created when results are completed
- **Download access**: Available to candidates and administrators

### Public Access Features
- **Public search**: Search by passport/birth certificate number
- **Detailed results view**: Complete score breakdown with explanations
- **AI feedback display**: Integrated feedback on public pages
- **Certificate verification**: Public verification by serial number
- **No authentication required**: Open access to completed results

## 6. API Endpoints

### Public Endpoints (No Authentication)
- `POST /api/search` - Search test results
- `GET /api/results/[id]` - Get detailed test results
- `GET /api/feedback/[resultId]` - Get AI feedback
- `GET /api/certificate/[id]` - Download certificate
- `GET /api/certificate/verify/[serial]` - Verify certificate
- `POST /api/results/[id]/generate-certificate` - Generate certificate
- `GET /api/health` - Health check

### Admin Endpoints (Admin Role Required)
- `GET /api/admin/dashboard` - Dashboard statistics
- `GET /api/admin/candidates` - List candidates
- `POST /api/admin/candidates` - Create candidate
- `GET /api/admin/results` - List all results
- `POST /api/admin/search` - Advanced search
- `GET /api/admin/export` - Export data

### Test Checker Endpoints (Admin + Test Checker)
- `GET /api/checker/dashboard` - Checker dashboard
- `GET /api/checker/candidates` - List candidates for entry
- `POST /api/checker/results` - Create/update results
- `GET /api/checker/results` - List results for checking

### AI Integration Endpoints
- `POST /api/ai/generate-feedback` - Generate AI feedback
- `POST /api/ai/save-feedback` - Save feedback to database

## 7. Key Components

### Layout Components
- `AdminLayout` - Admin dashboard wrapper with role checking
- `DashboardLayout` - Test checker interface wrapper
- Root layout with SessionProvider

### Form Components
- Candidate registration forms with photo upload
- Result entry forms with validation
- Search interfaces with filtering

### Display Components
- Result tables with pagination
- Score displays with band explanations
- Certificate preview and download
- AI feedback presentation

### Chart Components
- Performance analytics charts
- Score distribution visualizations

## 8. Authentication & Authorization

### Authentication Flow
1. **Credentials-based login** via NextAuth.js
2. **Password verification** with bcrypt
3. **JWT token generation** with user role
4. **Session management** with secure cookies

### Authorization Levels
- **Public**: Search, results viewing, certificate verification
- **Test Checker**: Result entry, candidate management, feedback generation
- **Admin**: Full system access, user management, reports, exports

### Route Protection
- **Middleware-based** protection for authenticated routes
- **Component-level** role checking in layouts
- **API-level** authorization in route handlers

## 9. External Integrations

### Anthropic Claude API
- **Model**: Claude 3 Sonnet (claude-3-sonnet-20240229)
- **Usage**: Personalized feedback generation
- **Fallback**: Mock feedback when API unavailable
- **Rate limiting**: Handled with error responses

### Database (Neon PostgreSQL)
- **Connection**: postgres-js driver with Drizzle ORM
- **Migrations**: Managed via Drizzle Kit
- **Connection pooling**: Optimized for serverless deployment

## 10. Deployment Configuration

### Environment Variables
```env
# Core Application
NEXTAUTH_SECRET=your-secret-key-here
NEXTAUTH_URL=http://localhost:3000
DATABASE_URL=postgresql://connection-string

# AI Integration
ANTHROPIC_API_KEY=your-claude-api-key

# Admin Credentials
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123

# Public URLs
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### Configuration Files
- **next.config.ts**: Next.js configuration with postgres external packages
- **drizzle.config.ts**: Database migration configuration
- **tailwind.config.ts**: Styling configuration
- **tsconfig.json**: TypeScript configuration

### Deployment Platform
- **Optimized for Vercel**: Serverless deployment ready
- **Database**: Neon PostgreSQL with connection pooling
- **Static assets**: Next.js optimization with CDN
- **Environment**: Production-ready with health checks

### Database Setup Scripts
- `npm run db:generate` - Generate migrations
- `npm run db:migrate` - Apply migrations
- `npm run db:setup` - Initialize users and data
- `npm run db:studio` - Database management interface

This analysis provides a comprehensive overview of the current IELTS system architecture, ready for planning the recreation with specified modifications.
