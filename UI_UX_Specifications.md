# Enhanced IELTS System - UI/UX Specifications

## 🎨 Design System Overview

### Color Palette
```css
:root {
  /* Primary Colors */
  --primary-blue: #0066CC;
  --primary-blue-light: #3399FF;
  --primary-blue-dark: #004499;
  
  /* Secondary Colors */
  --secondary-green: #00AA44;
  --secondary-orange: #FF6600;
  --secondary-red: #CC0000;
  
  /* Neutral Colors */
  --gray-50: #F9FAFB;
  --gray-100: #F3F4F6;
  --gray-200: #E5E7EB;
  --gray-300: #D1D5DB;
  --gray-400: #9CA3AF;
  --gray-500: #6B7280;
  --gray-600: #4B5563;
  --gray-700: #374151;
  --gray-800: #1F2937;
  --gray-900: #111827;
  
  /* Paywall Colors */
  --paywall-gold: #FFD700;
  --paywall-premium: #8B5CF6;
  --paywall-overlay: rgba(0, 0, 0, 0.8);
}
```

### Typography
```css
/* Font Families */
--font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
--font-heading: 'Poppins', sans-serif;
--font-mono: 'JetBrains Mono', monospace;

/* Font Sizes */
--text-xs: 0.75rem;    /* 12px */
--text-sm: 0.875rem;   /* 14px */
--text-base: 1rem;     /* 16px */
--text-lg: 1.125rem;   /* 18px */
--text-xl: 1.25rem;    /* 20px */
--text-2xl: 1.5rem;    /* 24px */
--text-3xl: 1.875rem;  /* 30px */
--text-4xl: 2.25rem;   /* 36px */
```

---

## 🏠 Master Admin Interface

### Organization Management Dashboard
```typescript
interface MasterDashboardLayout {
  header: {
    logo: string;
    title: "IELTS Master Control";
    userMenu: {
      avatar: string;
      name: "Master Admin";
      actions: ["Settings", "Logout"];
    };
  };
  
  sidebar: {
    sections: [
      {
        title: "Organizations";
        items: ["All Organizations", "Create New", "Billing"];
      },
      {
        title: "System";
        items: ["Analytics", "Health", "Logs"];
      },
      {
        title: "Settings";
        items: ["Global Config", "Security", "Backups"];
      }
    ];
  };
  
  mainContent: {
    breadcrumbs: string[];
    pageTitle: string;
    actionButtons: ActionButton[];
    content: React.ReactNode;
  };
}
```

### Organization List View
```typescript
interface OrganizationListView {
  filters: {
    status: "all" | "active" | "suspended" | "disabled";
    billingPlan: "all" | "basic" | "premium" | "enterprise";
    searchQuery: string;
  };
  
  organizationCards: {
    id: string;
    name: string;
    slug: string;
    status: StatusBadge;
    metrics: {
      candidates: number;
      tests: number;
      revenue: number;
      growth: number;
    };
    actions: ["View", "Edit", "Suspend", "Delete"];
  }[];
  
  pagination: PaginationComponent;
}
```

---

## 💳 Paywall Interface Components

### Paywall Overlay Design
```typescript
interface PaywallOverlay {
  backdrop: {
    className: "fixed inset-0 bg-paywall-overlay z-50";
    blur: "backdrop-blur-sm";
  };
  
  modal: {
    position: "center";
    maxWidth: "500px";
    background: "white";
    borderRadius: "16px";
    padding: "32px";
    shadow: "xl";
  };
  
  content: {
    icon: LockIcon | CrownIcon;
    title: string; // "Unlock AI Feedback" | "Download Certificate"
    description: string;
    features: string[]; // What they get
    pricing: {
      amount: number;
      currency: string;
      originalPrice?: number; // for discounts
    };
    promotionalBanner?: {
      text: string;
      color: "green" | "blue" | "purple";
    };
  };
  
  actions: {
    primaryButton: {
      text: "Unlock Now";
      color: "paywall-premium";
      onClick: () => void;
    };
    secondaryButton: {
      text: "Maybe Later";
      color: "gray";
      onClick: () => void;
    };
  };
}
```

### Payment Gateway Selection
```typescript
interface PaymentGatewaySelector {
  title: "Choose Payment Method";
  
  gateways: [
    {
      id: "click";
      name: "Click";
      logo: "/images/click-logo.png";
      description: "Pay with Click";
      fees: "No additional fees";
      selected: boolean;
    },
    {
      id: "payme";
      name: "Payme";
      logo: "/images/payme-logo.png";
      description: "Pay with Payme";
      fees: "No additional fees";
      selected: boolean;
    }
  ];
  
  orderSummary: {
    item: string;
    price: number;
    discount?: number;
    total: number;
  };
  
  proceedButton: {
    text: "Proceed to Payment";
    disabled: boolean;
    loading: boolean;
  };
}
```

---

## 📊 Public Results Interface

### Tabbed Results Layout
```typescript
interface PublicResultsLayout {
  header: {
    breadcrumbs: ["Search", "Results"];
    candidateInfo: {
      name: string;
      testDate: string;
      testCenter: string;
    };
    backButton: {
      text: "Back to Search";
      href: "/search";
    };
  };
  
  tabNavigation: {
    tabs: [
      {
        id: "results";
        label: "Test Results";
        icon: FileTextIcon;
        accessible: true;
        badge?: string;
      },
      {
        id: "progress";
        label: "Progress History";
        icon: TrendingUpIcon;
        accessible: true;
        badge?: string;
      },
      {
        id: "feedback";
        label: "AI Feedback";
        icon: MessageSquareIcon;
        accessible: boolean;
        badge?: "Premium";
        lockIcon?: boolean;
      },
      {
        id: "certificate";
        label: "Certificate";
        icon: AwardIcon;
        accessible: boolean;
        badge?: "Premium";
        lockIcon?: boolean;
      }
    ];
    activeTab: string;
  };
  
  tabContent: {
    [key: string]: React.ReactNode;
  };
}
```

### Results Tab Content
```typescript
interface ResultsTabContent {
  scoreOverview: {
    overallBand: {
      score: number;
      description: string;
      color: string;
    };
    skillBreakdown: {
      listening: ScoreCard;
      reading: ScoreCard;
      writing: ScoreCard;
      speaking: ScoreCard;
    };
  };
  
  detailedScores: {
    sections: [
      {
        title: "Listening";
        rawScore: number;
        bandScore: number;
        breakdown?: ComponentScore[];
      },
      {
        title: "Reading";
        rawScore: number;
        bandScore: number;
        breakdown?: ComponentScore[];
      },
      {
        title: "Writing";
        task1Score: number;
        task2Score: number;
        bandScore: number;
      },
      {
        title: "Speaking";
        components: {
          fluency: number;
          lexical: number;
          grammar: number;
          pronunciation: number;
        };
        bandScore: number;
      }
    ];
  };
  
  bandDescriptors: {
    expandable: boolean;
    content: BandDescriptionCard[];
  };
}
```

### Progress Tab Content
```typescript
interface ProgressTabContent {
  progressChart: {
    type: "line";
    data: {
      labels: string[]; // test dates
      datasets: [
        {
          label: "Overall Band Score";
          data: number[];
          borderColor: "primary-blue";
          backgroundColor: "primary-blue-light";
        },
        {
          label: "Listening";
          data: number[];
          borderColor: "secondary-green";
        },
        {
          label: "Reading";
          data: number[];
          borderColor: "secondary-orange";
        },
        {
          label: "Writing";
          data: number[];
          borderColor: "secondary-red";
        },
        {
          label: "Speaking";
          data: number[];
          borderColor: "primary-blue-dark";
        }
      ];
    };
    options: ChartOptions;
  };
  
  testHistory: {
    timeline: TestHistoryItem[];
    summary: {
      totalTests: number;
      bestScore: number;
      averageImprovement: number;
      mostImprovedSkill: string;
    };
  };
  
  achievements: {
    badges: AchievementBadge[];
    milestones: MilestoneCard[];
  };
}
```

### Feedback Tab (Paywall Protected)
```typescript
interface FeedbackTabContent {
  accessStatus: "granted" | "paywall" | "promotional";
  
  paywallContent?: {
    blurredPreview: {
      backgroundImage: "url(data:image/...)"; // blurred feedback
      overlay: PaywallOverlay;
    };
    unlockButton: {
      text: "Unlock AI Feedback";
      price: number;
      promotionalOffer?: string;
    };
  };
  
  feedbackContent?: {
    overallAssessment: {
      title: "Overall Assessment";
      content: string;
      score: number;
      level: string;
    };
    
    skillFeedback: {
      listening: SkillFeedbackCard;
      reading: SkillFeedbackCard;
      writing: SkillFeedbackCard;
      speaking: SkillFeedbackCard;
    };
    
    recommendations: {
      strengths: string[];
      improvements: string[];
      studyPlan: StudyPlanCard;
      nextSteps: string[];
    };
  };
}
```

### Certificate Tab (Paywall Protected)
```typescript
interface CertificateTabContent {
  accessStatus: "granted" | "paywall" | "promotional" | "expired";
  
  paywallContent?: {
    certificatePreview: {
      thumbnail: string; // blurred certificate image
      overlay: PaywallOverlay;
    };
    unlockButton: {
      text: "Download Certificate";
      price: number;
      promotionalOffer?: string;
    };
    features: [
      "Official IELTS Certificate",
      "PDF Download",
      "Verification QR Code",
      "Valid for 6 months"
    ];
  };
  
  certificateContent?: {
    status: CertificateStatusBadge;
    expirationInfo: {
      expiresAt: string;
      daysRemaining: number;
      warningLevel: "none" | "warning" | "critical";
    };
    downloadButton: {
      text: "Download PDF";
      onClick: () => void;
    };
    verificationInfo: {
      serialNumber: string;
      qrCode: string;
      verificationUrl: string;
    };
  };
}
```

---

## 🎁 Promotional System Interface

### Promotion Banner Component
```typescript
interface PromotionBanner {
  type: "student_discount" | "loyalty_reward" | "limited_time";
  
  design: {
    background: "gradient" | "solid";
    colors: string[];
    animation: "pulse" | "slide" | "none";
  };
  
  content: {
    icon: React.ReactNode;
    title: string;
    description: string;
    ctaButton: {
      text: string;
      action: () => void;
    };
  };
  
  dismissible: boolean;
  autoHide?: number; // seconds
}
```

### Student Status Indicator
```typescript
interface StudentStatusIndicator {
  isStudent: boolean;
  
  badge: {
    text: "Student" | "Regular";
    color: "green" | "blue";
    icon: GraduationCapIcon | UserIcon;
  };
  
  benefits?: {
    title: "Student Benefits";
    items: string[];
  };
}
```

---

## 📱 Responsive Design Specifications

### Breakpoints
```css
/* Mobile First Approach */
@media (min-width: 640px) { /* sm */ }
@media (min-width: 768px) { /* md */ }
@media (min-width: 1024px) { /* lg */ }
@media (min-width: 1280px) { /* xl */ }
@media (min-width: 1536px) { /* 2xl */ }
```

### Mobile Optimizations
- **Touch-friendly buttons**: Minimum 44px touch targets
- **Swipe gestures**: Tab navigation with swipe support
- **Collapsible sections**: Accordion-style content organization
- **Bottom sheet modals**: Mobile-optimized payment flows
- **Sticky headers**: Keep navigation accessible while scrolling

### Accessibility Features
- **WCAG 2.1 AA compliance**: Color contrast, keyboard navigation
- **Screen reader support**: Proper ARIA labels and descriptions
- **Focus management**: Clear focus indicators and logical tab order
- **Alternative text**: Comprehensive alt text for images and charts
- **Reduced motion**: Respect user preferences for animations

This UI/UX specification provides comprehensive design guidelines for implementing all enhanced features with a focus on user experience, accessibility, and responsive design.
