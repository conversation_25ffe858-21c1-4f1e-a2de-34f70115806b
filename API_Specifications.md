# Enhanced IELTS System - API Specifications

## 🌐 API Architecture Overview

### Base URL Structure
```
Production: https://ielts-system.com/api
Development: http://localhost:3000/api
```

### Authentication
- **JWT Tokens**: Bearer token authentication
- **Organization Context**: All requests include organization context
- **Master Admin**: Special authentication for system-wide access

---

## 🏢 Master Admin Endpoints

### Organization Management
```typescript
// GET /api/master/organizations
// List all organizations with usage statistics
interface OrganizationListResponse {
  organizations: {
    id: string;
    name: string;
    slug: string;
    status: 'active' | 'suspended' | 'disabled';
    totalCandidates: number;
    totalTests: number;
    monthlyRevenue: number;
    createdAt: string;
  }[];
  pagination: PaginationInfo;
}

// POST /api/master/organizations
// Create new organization
interface CreateOrganizationRequest {
  name: string;
  slug: string;
  adminEmail: string;
  adminPassword: string;
  features: string[]; // enabled features
  billingPlan: string;
}

// PUT /api/master/organizations/[id]
// Update organization settings
interface UpdateOrganizationRequest {
  name?: string;
  status?: 'active' | 'suspended' | 'disabled';
  features?: string[];
  billingPlan?: string;
}

// GET /api/master/analytics
// System-wide analytics
interface MasterAnalyticsResponse {
  totalOrganizations: number;
  totalCandidates: number;
  totalRevenue: number;
  monthlyGrowth: number;
  topPerformingOrgs: OrganizationStats[];
  systemHealth: SystemHealthMetrics;
}
```

---

## 💳 Payment Processing Endpoints

### Payment Initiation
```typescript
// POST /api/payments/initiate
// Start payment process for premium features
interface PaymentInitiationRequest {
  candidateId: string;
  resultId: string;
  featureType: 'feedback' | 'certificate';
  gateway: 'click' | 'payme';
  amount: number;
  currency: string;
}

interface PaymentInitiationResponse {
  transactionId: string;
  paymentUrl: string;
  expiresAt: string;
  amount: number;
  gateway: string;
}

// POST /api/payments/webhook/click
// Click payment webhook handler
interface ClickWebhookRequest {
  click_trans_id: string;
  service_id: string;
  click_paydoc_id: string;
  merchant_trans_id: string;
  amount: number;
  action: number;
  error: number;
  error_note: string;
  sign_time: string;
  sign_string: string;
}

// POST /api/payments/webhook/payme
// Payme payment webhook handler
interface PaymeWebhookRequest {
  method: string;
  params: {
    id: string;
    time: number;
    amount: number;
    account: {
      candidate_id: string;
      result_id: string;
      feature_type: string;
    };
  };
}
```

### Payment Management
```typescript
// GET /api/payments/history/[candidateId]
// Get payment history for candidate
interface PaymentHistoryResponse {
  transactions: {
    id: string;
    amount: number;
    currency: string;
    gateway: string;
    status: string;
    featureType: string;
    resultId: string;
    createdAt: string;
    completedAt?: string;
  }[];
  totalSpent: number;
  totalTransactions: number;
}

// POST /api/payments/manual-approve
// Manual payment approval by admin
interface ManualPaymentApprovalRequest {
  transactionId: string;
  adminNotes?: string;
  approvedAmount?: number;
}

// POST /api/payments/refund
// Process payment refund
interface PaymentRefundRequest {
  transactionId: string;
  reason: string;
  refundAmount?: number; // partial refund support
}
```

---

## 🎁 Promotional System Endpoints

### Promotion Management
```typescript
// GET /api/promotions/rules
// List promotional rules for organization
interface PromotionalRulesResponse {
  rules: {
    id: string;
    name: string;
    type: string;
    featureType: string;
    criteria: Record<string, any>;
    benefits: Record<string, any>;
    status: string;
    usageCount: number;
    usageLimit?: number;
  }[];
}

// POST /api/promotions/rules
// Create new promotional rule
interface CreatePromotionalRuleRequest {
  name: string;
  type: 'student_discount' | 'loyalty_reward' | 'time_based' | 'custom';
  featureType: 'feedback' | 'certificate' | 'both';
  criteria: {
    studentStatus?: boolean;
    minTests?: number;
    timeRange?: { start: string; end: string };
    customConditions?: Record<string, any>;
  };
  benefits: {
    discountPercent?: number;
    freeAccess?: boolean;
    validityDays?: number;
  };
  usageLimit?: number;
}

// POST /api/promotions/check-eligibility
// Check if candidate is eligible for promotions
interface PromotionEligibilityRequest {
  candidateId: string;
  featureType: 'feedback' | 'certificate';
  resultId: string;
}

interface PromotionEligibilityResponse {
  eligible: boolean;
  availablePromotions: {
    ruleId: string;
    name: string;
    benefit: string;
    description: string;
  }[];
  recommendedPromotion?: string;
}
```

---

## 👤 Enhanced Candidate Management

### Candidate Progress Tracking
```typescript
// GET /api/candidates/[id]/progress
// Get comprehensive candidate progress
interface CandidateProgressResponse {
  candidate: {
    id: string;
    fullName: string;
    totalTests: number;
    studentStatus: boolean;
    registrationDate: string;
  };
  testHistory: {
    id: string;
    testDate: string;
    overallBandScore: number;
    skillScores: {
      listening: number;
      reading: number;
      writing: number;
      speaking: number;
    };
    improvement: number; // compared to previous test
    rank: number; // among all tests
  }[];
  progressMetrics: {
    averageImprovement: number;
    bestScore: number;
    mostImprovedSkill: string;
    consistencyScore: number;
  };
  recommendations: string[];
}

// GET /api/candidates/[id]/results/selectable
// Get results available for feature unlocking
interface SelectableResultsResponse {
  results: {
    id: string;
    testDate: string;
    overallBandScore: number;
    skillScores: Record<string, number>;
    isRecommended: boolean; // best score
    hasUnlockedFeedback: boolean;
    hasUnlockedCertificate: boolean;
  }[];
  bestResult: string; // result ID with highest score
}
```

---

## 📋 Public Results Interface

### Tabbed Results Access
```typescript
// GET /api/public/results/[id]
// Get public results with tab information
interface PublicResultsResponse {
  result: {
    id: string;
    candidateInfo: {
      fullName: string;
      testDate: string;
      testCenter: string;
    };
    scores: {
      listening: number;
      reading: number;
      writing: number;
      speaking: number;
      overall: number;
    };
  };
  tabs: {
    results: { accessible: true; content: TestResultDetails };
    progress: { accessible: true; content: ProgressSummary };
    feedback: { 
      accessible: boolean; 
      content: AIFeedback | PaywallInfo;
      unlockPrice?: number;
    };
    certificate: { 
      accessible: boolean; 
      content: CertificateInfo | PaywallInfo;
      unlockPrice?: number;
    };
  };
}

// GET /api/public/results/[id]/progress
// Get candidate progress for public viewing
interface PublicProgressResponse {
  testHistory: {
    testDate: string;
    overallScore: number;
    improvement: number;
  }[];
  progressChart: {
    labels: string[];
    datasets: {
      label: string;
      data: number[];
      borderColor: string;
    }[];
  };
  achievements: {
    title: string;
    description: string;
    achievedAt: string;
  }[];
}
```

---

## 📜 Certificate Lifecycle Management

### Certificate Operations
```typescript
// GET /api/certificates/[resultId]/status
// Check certificate status and expiration
interface CertificateStatusResponse {
  status: 'not_generated' | 'active' | expired' | 'deleted';
  generatedAt?: string;
  expiresAt?: string;
  daysUntilExpiration?: number;
  canRegenerate: boolean;
  unlockRequired: boolean;
}

// POST /api/certificates/[resultId]/generate
// Generate certificate (after payment verification)
interface CertificateGenerationRequest {
  resultId: string;
  paymentTransactionId?: string; // if payment required
  promotionRuleId?: string; // if using promotion
}

// GET /api/certificates/[resultId]/download
// Download certificate PDF
// Returns: PDF file stream

// POST /api/certificates/extend-expiration
// Extend certificate expiration (admin only)
interface ExtendExpirationRequest {
  resultId: string;
  newExpirationDate: string;
  reason: string;
}
```

---

## 🔍 Enhanced Search & Analytics

### Advanced Search
```typescript
// POST /api/search/advanced
// Advanced search with filters
interface AdvancedSearchRequest {
  query?: string;
  filters: {
    organizationId?: string;
    testDateRange?: { start: string; end: string };
    scoreRange?: { min: number; max: number };
    paymentStatus?: 'paid' | 'unpaid' | 'promotional';
    studentStatus?: boolean;
  };
  pagination: {
    page: number;
    limit: number;
  };
  sortBy?: 'testDate' | 'score' | 'name';
  sortOrder?: 'asc' | 'desc';
}

// GET /api/analytics/organization/[id]
// Organization-specific analytics
interface OrganizationAnalyticsResponse {
  overview: {
    totalCandidates: number;
    totalTests: number;
    totalRevenue: number;
    conversionRate: number;
  };
  trends: {
    monthlyTests: number[];
    monthlyRevenue: number[];
    labels: string[];
  };
  topPerformers: {
    candidateName: string;
    bestScore: number;
    totalTests: number;
  }[];
  promotionEffectiveness: {
    ruleName: string;
    usageCount: number;
    conversionRate: number;
  }[];
}
```

This API specification provides comprehensive endpoints for all enhanced features of the IELTS system, including payment processing, promotional systems, and advanced candidate management.
