# Enhanced IELTS System - Technical Specifications

## 🗄️ Database Schema Design

### Core Tables

#### Organizations Table
```sql
CREATE TABLE organizations (
  id TEXT PRIMARY KEY DEFAULT cuid(),
  name TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  settings JSONB DEFAULT '{}',
  features J<PERSON><PERSON><PERSON> DEFAULT '{}', -- enabled features per org
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'suspended', 'disabled')),
  billing_plan TEXT DEFAULT 'basic',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### Enhanced Candidates Table
```sql
CREATE TABLE candidates (
  id TEXT PRIMARY KEY DEFAULT cuid(),
  organization_id TEXT REFERENCES organizations(id),
  full_name TEXT NOT NULL,
  email TEXT NOT NULL,
  phone_number TEXT NOT NULL,
  date_of_birth DATE NOT NULL,
  nationality TEXT NOT NULL,
  passport_number TEXT NOT NULL, -- unique within organization
  photo_data TEXT, -- base64 encoded
  student_status BOOLEAN DEFAULT FALSE, -- for promotional rules
  total_tests INTEGER DEFAULT 0,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(organization_id, passport_number)
);
```

#### Payment Transactions Table
```sql
CREATE TABLE payment_transactions (
  id TEXT PRIMARY KEY DEFAULT cuid(),
  candidate_id TEXT REFERENCES candidates(id),
  organization_id TEXT REFERENCES organizations(id),
  amount DECIMAL(10,2) NOT NULL,
  currency TEXT DEFAULT 'UZS',
  gateway TEXT NOT NULL CHECK (gateway IN ('click', 'payme', 'manual')),
  gateway_transaction_id TEXT,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'failed', 'refunded')),
  feature_type TEXT NOT NULL CHECK (feature_type IN ('feedback', 'certificate')),
  result_id TEXT REFERENCES test_results(id),
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW(),
  completed_at TIMESTAMP
);
```

#### Access Permissions Table
```sql
CREATE TABLE access_permissions (
  id TEXT PRIMARY KEY DEFAULT cuid(),
  candidate_id TEXT REFERENCES candidates(id),
  result_id TEXT REFERENCES test_results(id),
  feature_type TEXT NOT NULL CHECK (feature_type IN ('feedback', 'certificate')),
  access_type TEXT NOT NULL CHECK (access_type IN ('paid', 'promotional', 'manual')),
  granted_by TEXT REFERENCES users(id), -- for manual grants
  granted_at TIMESTAMP DEFAULT NOW(),
  expires_at TIMESTAMP, -- for time-limited access
  metadata JSONB DEFAULT '{}' -- promotion details, payment info, etc.
);
```

#### Promotional Rules Table
```sql
CREATE TABLE promotional_rules (
  id TEXT PRIMARY KEY DEFAULT cuid(),
  organization_id TEXT REFERENCES organizations(id),
  name TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('student_discount', 'loyalty_reward', 'time_based', 'custom')),
  feature_type TEXT NOT NULL CHECK (feature_type IN ('feedback', 'certificate', 'both')),
  criteria JSONB NOT NULL, -- rule conditions
  benefits JSONB NOT NULL, -- what user gets
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
  valid_from TIMESTAMP,
  valid_until TIMESTAMP,
  usage_limit INTEGER, -- max uses per candidate
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### Certificate Lifecycle Table
```sql
CREATE TABLE certificate_lifecycle (
  id TEXT PRIMARY KEY DEFAULT cuid(),
  result_id TEXT UNIQUE REFERENCES test_results(id),
  generated_at TIMESTAMP,
  expires_at TIMESTAMP NOT NULL, -- 6 months from test date
  status TEXT DEFAULT 'not_generated' CHECK (status IN ('not_generated', 'active', 'expired', 'deleted')),
  deletion_scheduled_at TIMESTAMP,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

## 🔐 Authentication & Authorization

### User Roles Hierarchy
```typescript
enum UserRole {
  MASTER_ADMIN = 'master_admin',     // System-wide control
  ORG_ADMIN = 'org_admin',           // Organization admin
  TEST_CHECKER = 'test_checker',     // Test result entry
  CANDIDATE = 'candidate'            // Limited self-service
}

interface UserPermissions {
  organizations: {
    create: boolean;
    read: boolean;
    update: boolean;
    delete: boolean;
  };
  candidates: {
    create: boolean;
    read: boolean;
    update: boolean;
    delete: boolean;
  };
  payments: {
    process: boolean;
    approve: boolean;
    refund: boolean;
    view: boolean;
  };
  promotions: {
    create: boolean;
    manage: boolean;
    override: boolean;
  };
}
```

### Master Admin Configuration
```typescript
// Hardcoded master admin credentials (environment variables)
const MASTER_ADMIN_CREDENTIALS = {
  email: process.env.MASTER_ADMIN_EMAIL || '<EMAIL>',
  password: process.env.MASTER_ADMIN_PASSWORD || 'MasterAdmin2024!',
  role: 'master_admin'
};
```

## 💳 Payment Integration

### Click Payment API Integration
```typescript
interface ClickPaymentRequest {
  service_id: string;
  click_trans_id: string;
  merchant_trans_id: string;
  amount: number;
  action: number; // 0 = prepare, 1 = complete
  error: number;
  error_note: string;
  sign_time: string;
  sign_string: string;
}

class ClickPaymentService {
  async preparePayment(amount: number, candidateId: string, featureType: string): Promise<string>;
  async completePayment(transactionId: string): Promise<boolean>;
  async verifySignature(request: ClickPaymentRequest): Promise<boolean>;
}
```

### Payme Payment API Integration
```typescript
interface PaymePaymentRequest {
  method: string;
  params: {
    id: string;
    time: number;
    amount: number;
    account: {
      candidate_id: string;
      feature_type: string;
    };
  };
}

class PaymePaymentService {
  async createTransaction(amount: number, candidateId: string): Promise<string>;
  async performTransaction(transactionId: string): Promise<boolean>;
  async cancelTransaction(transactionId: string): Promise<boolean>;
}
```

## 🎁 Promotional System

### Rule Engine Configuration
```typescript
interface PromotionalRule {
  id: string;
  organizationId: string;
  name: string;
  type: 'student_discount' | 'loyalty_reward' | 'time_based' | 'custom';
  featureType: 'feedback' | 'certificate' | 'both';
  criteria: {
    studentStatus?: boolean;
    minTests?: number;
    timeRange?: { start: Date; end: Date };
    customConditions?: Record<string, any>;
  };
  benefits: {
    discountPercent?: number;
    freeAccess?: boolean;
    validityDays?: number;
  };
  status: 'active' | 'inactive';
  usageLimit?: number;
}

class PromotionalEngine {
  async checkEligibility(candidateId: string, featureType: string): Promise<PromotionalRule[]>;
  async applyPromotion(candidateId: string, ruleId: string, resultId: string): Promise<boolean>;
  async validatePromotion(ruleId: string, candidateId: string): Promise<boolean>;
}
```

## 📊 Public Results Interface

### Tabbed Interface Structure
```typescript
interface PublicResultsPage {
  tabs: {
    results: {
      accessible: true;
      content: TestResultDetails;
    };
    progress: {
      accessible: true;
      content: CandidateProgressHistory;
    };
    feedback: {
      accessible: boolean; // based on payment/promotion
      content: AIFeedback | PaywallOverlay;
    };
    certificate: {
      accessible: boolean; // based on payment/promotion
      content: CertificatePreview | PaywallOverlay;
    };
  };
}

interface PaywallOverlay {
  type: 'feedback' | 'certificate';
  blurredContent: string; // base64 blurred image
  unlockButton: {
    text: string;
    action: 'payment' | 'promotion';
    price?: number;
    promotionText?: string;
  };
}
```

## 🔄 Scheduled Jobs

### Certificate Lifecycle Management
```typescript
class CertificateLifecycleJob {
  // Run daily at 2 AM
  @Cron('0 2 * * *')
  async processExpiredCertificates(): Promise<void> {
    // Mark certificates as expired (6 months from test date)
    await this.markExpiredCertificates();
    
    // Schedule deletion (30 days after expiration)
    await this.scheduleExpiredCertificateDeletion();
    
    // Delete certificates scheduled for deletion
    await this.deleteScheduledCertificates();
    
    // Send expiration warnings (30, 7, 1 days before)
    await this.sendExpirationWarnings();
  }
}
```

### Payment Verification Job
```typescript
class PaymentVerificationJob {
  // Run every 15 minutes
  @Cron('*/15 * * * *')
  async verifyPendingPayments(): Promise<void> {
    const pendingPayments = await this.getPendingPayments();
    
    for (const payment of pendingPayments) {
      const status = await this.verifyPaymentStatus(payment);
      if (status === 'completed') {
        await this.grantAccess(payment);
      }
    }
  }
}
```

## 📈 Analytics & Reporting

### Organization Analytics
```typescript
interface OrganizationAnalytics {
  totalCandidates: number;
  totalTests: number;
  totalRevenue: number;
  conversionRates: {
    feedbackUnlocks: number;
    certificateUnlocks: number;
  };
  popularFeatures: string[];
  monthlyGrowth: number;
  promotionUsage: Record<string, number>;
}

class AnalyticsService {
  async generateOrganizationReport(orgId: string, period: string): Promise<OrganizationAnalytics>;
  async generateRevenueReport(orgId: string, period: string): Promise<RevenueReport>;
  async generateCandidateProgressReport(candidateId: string): Promise<ProgressReport>;
}
```

## 🔒 Security Measures

### Data Isolation
```typescript
// Middleware for organization data isolation
class OrganizationIsolationMiddleware {
  async isolateData(req: Request, res: Response, next: NextFunction): Promise<void> {
    const userOrgId = req.user?.organizationId;
    const requestedOrgId = req.params.organizationId || req.body.organizationId;
    
    if (req.user?.role !== 'master_admin' && userOrgId !== requestedOrgId) {
      throw new ForbiddenError('Access denied to organization data');
    }
    
    next();
  }
}
```

### Payment Security
```typescript
class PaymentSecurityService {
  async validatePaymentSignature(gateway: string, payload: any): Promise<boolean>;
  async encryptSensitiveData(data: string): Promise<string>;
  async auditPaymentTransaction(transactionId: string, action: string): Promise<void>;
  async detectFraudulentActivity(candidateId: string): Promise<boolean>;
}
```

This technical specification provides the detailed implementation blueprint for building the enhanced IELTS system with all requested features.
